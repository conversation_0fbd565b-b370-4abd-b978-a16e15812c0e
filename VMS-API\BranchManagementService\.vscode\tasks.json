{"version": "2.0.0", "tasks": [{"label": "build-branch", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder:BranchManagementService}/BranchManagementService.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish-branch", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder:BranchManagementService}/BranchManagementService.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch-branch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder:BranchManagementService}/src/BranchManagementService.API/BranchManagementService.API.csproj", "--urls", "http://localhost:5004"], "problemMatcher": "$msCompile"}, {"label": "ef-migrations-add-branch", "command": "dotnet", "type": "process", "args": ["ef", "migrations", "add", "${input:migrationName}", "--project", "${workspaceFolder:BranchManagementService}/src/BranchManagementService.Infrastructure/BranchManagementService.Infrastructure.csproj", "--startup-project", "${workspaceFolder:BranchManagementService}/src/BranchManagementService.API/BranchManagementService.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "ef-database-update-branch", "command": "dotnet", "type": "process", "args": ["ef", "database", "update", "--project", "${workspaceFolder:BranchManagementService}/src/BranchManagementService.Infrastructure/BranchManagementService.Infrastructure.csproj", "--startup-project", "${workspaceFolder:BranchManagementService}/src/BranchManagementService.API/BranchManagementService.API.csproj"], "problemMatcher": "$msCompile"}], "inputs": [{"id": "migrationName", "type": "promptString", "description": "Migration Name", "default": "NewMigration"}]}