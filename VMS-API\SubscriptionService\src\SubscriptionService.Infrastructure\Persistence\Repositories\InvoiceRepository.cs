using Microsoft.EntityFrameworkCore;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.Infrastructure.Persistence.Repositories
{
    public class InvoiceRepository : DomainBaseRepository<Invoice>, IInvoiceRepository
    {
        public InvoiceRepository(SubscriptionDbContext context) : base(context)
        {
        }

        public async Task<IReadOnlyList<Invoice>> GetInvoicesBySubscriptionIdAsync(Guid subscriptionId)
        {
            return await _dbSet
                .Where(i => i.SubscriptionId == subscriptionId)
                .OrderByDescending(i => i.IssueDate)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Invoice>> GetInvoicesByUserIdAsync(Guid userId)
        {
            return await _dbSet
                .Include(i => i.Subscription)
                .Where(i => i.Subscription.UserId == userId)
                .OrderByDescending(i => i.IssueDate)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status)
        {
            return await _dbSet
                .Where(i => i.Status == status)
                .OrderByDescending(i => i.IssueDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber)
        {
            return await _dbSet
                .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        public async Task<IReadOnlyList<Invoice>> GetOverdueInvoicesAsync()
        {
            var today = DateTime.UtcNow.Date;
            return await _dbSet
                .Where(i => i.Status == InvoiceStatus.Pending && i.DueDate < today)
                .OrderBy(i => i.DueDate)
                .ToListAsync();
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var year = DateTime.UtcNow.Year;
            var month = DateTime.UtcNow.Month.ToString("D2");

            // Get the count of invoices for the current year and month
            var count = await _dbSet
                .CountAsync(i => i.InvoiceNumber.StartsWith($"INV-{year}{month}"));

            // Generate the invoice number in the format INV-YYYYMM-XXXX
            return $"INV-{year}{month}-{(count + 1).ToString("D4")}";
        }
    }
}
