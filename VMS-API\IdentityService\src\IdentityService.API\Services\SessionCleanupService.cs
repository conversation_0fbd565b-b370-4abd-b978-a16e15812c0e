using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Services;

/// <summary>
/// Background service to clean up expired and idle sessions
/// </summary>
public class SessionCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SessionCleanupService> _logger;
    private readonly TimeSpan _interval = TimeSpan.FromMinutes(5); // Run every 5 minutes

    public SessionCleanupService(
        IServiceProvider serviceProvider,
        ILogger<SessionCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Session cleanup service is starting");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CleanupSessionsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while cleaning up sessions");
            }

            await Task.Delay(_interval, stoppingToken);
        }

        _logger.LogInformation("Session cleanup service is stopping");
    }

    private async Task CleanupSessionsAsync()
    {
        _logger.LogDebug("Starting session cleanup");

        using var scope = _serviceProvider.CreateScope();
        var sessionManagementService = scope.ServiceProvider.GetRequiredService<ISessionManagementService>();

        await sessionManagementService.ExpireIdleSessionsAsync();

        _logger.LogDebug("Session cleanup completed");
    }
}
