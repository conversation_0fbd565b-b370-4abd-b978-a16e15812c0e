using System.Collections.Generic;
using IdentityService.Domain.Services;

namespace IdentityService.Application.Interfaces;

/// <summary>
/// Service for validating password complexity
/// </summary>
public interface IPasswordComplexityService
{
    /// <summary>
    /// Validates a password against the configured complexity requirements
    /// </summary>
    /// <param name="password">The password to validate</param>
    /// <param name="username">Optional username to check against (for preventing username in password)</param>
    /// <returns>A tuple with a boolean indicating if the password is valid and a list of validation errors</returns>
    (bool IsValid, IEnumerable<string> Errors) ValidatePassword(string password, string username = null);

    /// <summary>
    /// Gets the current password complexity options
    /// </summary>
    /// <returns>The current password complexity options</returns>
    PasswordComplexityOptions GetPasswordComplexityOptions();
}
