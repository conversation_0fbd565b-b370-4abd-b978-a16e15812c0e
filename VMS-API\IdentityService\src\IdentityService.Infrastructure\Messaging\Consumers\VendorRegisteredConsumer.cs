using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Infrastructure.Messaging.Events;
using MassTransit;
using Microsoft.Extensions.Logging;
using VMS.Contracts.ServiceClients;
using VMS.Contracts.Tenant.Events;

namespace IdentityService.Infrastructure.Messaging.Consumers;

/// <summary>
/// Consumer for VendorRegistered events
/// </summary>
public class VendorRegisteredConsumer : IConsumer<VendorRegistered>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IEmailService _emailService;
    private readonly IEventPublisher _eventPublisher;
    private readonly ITenantManagementServiceClient _tenantManagementServiceClient;
    private readonly ILogger<VendorRegisteredConsumer> _logger;

    public VendorRegisteredConsumer(
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        IEmailService emailService,
        IEventPublisher eventPublisher,
        ITenantManagementServiceClient tenantManagementServiceClient,
        ILogger<VendorRegisteredConsumer> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _passwordHasher = passwordHasher ?? throw new ArgumentNullException(nameof(passwordHasher));
        _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        _eventPublisher = eventPublisher ?? throw new ArgumentNullException(nameof(eventPublisher));
        _tenantManagementServiceClient = tenantManagementServiceClient ?? throw new ArgumentNullException(nameof(tenantManagementServiceClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Consume(ConsumeContext<VendorRegistered> context)
    {
        var @event = context.Message;

        _logger.LogInformation("Received VendorRegistered event for vendor {VendorId}", @event.VendorId);

        try
        {
            // Check if user with this email already exists
            var existingUser = await _unitOfWork.UserRepository.GetByEmailAsync(@event.Email);
            if (existingUser != null)
            {
                _logger.LogWarning("User with email {Email} already exists", @event.Email);
                return;
            }

            // Generate a random password
            var password = GenerateRandomPassword();
            var passwordHash = _passwordHasher.HashPassword(password);

            // Create a new user for the vendor
            var user = User.Create(
                @event.Email,
                @event.PhoneNumber,
                passwordHash,
                "System");

            // Add the user to the database
            await _unitOfWork.UserRepository.AddAsync(user);

            // Get the Vendor role
            var vendorRole = await GetOrCreateVendorRole();
            if (vendorRole != null)
            {
                // Assign the Vendor role to the user
                user.AddRole(vendorRole, "System");
            }
            else
            {
                _logger.LogWarning("Vendor role not found and could not be created");
            }

            // Save changes
            await _unitOfWork.SaveChangesAsync();

            // Update the vendor with the user ID
            try
            {
                _logger.LogInformation("Updating vendor {VendorId} with user ID {UserId}", @event.VendorId, user.Id);

                var success = await _tenantManagementServiceClient.UpdateVendorUserIdAsync(@event.VendorId, user.Id);

                if (success)
                {
                    _logger.LogInformation("Successfully updated vendor {VendorId} with user ID {UserId}", @event.VendorId, user.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to update vendor {VendorId} with user ID {UserId}", @event.VendorId, user.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating vendor {VendorId} with user ID {UserId}", @event.VendorId, user.Id);
                // Continue processing even if this fails
            }

            // Send email with credentials
            await SendCredentialsEmail(@event.Email, @event.BusinessName, @event.Email, password);

            // Publish UserCreated event
            await PublishUserCreatedEvent(user);

            _logger.LogInformation("Successfully created user {UserId} for vendor {VendorId}", user.Id, @event.VendorId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing VendorRegistered event for vendor {VendorId}", @event.VendorId);
        }
    }

    private async Task<Role> GetOrCreateVendorRole()
    {
        // Try to get the Vendor role
        var roles = await _unitOfWork.RoleRepository.GetAsync(r => r.Name == "Vendor");
        if (roles.Count > 0)
        {
            return roles[0];
        }

        // If the role doesn't exist, create it
        var vendorRole = Role.Create("Vendor", "Vendor role for accessing vendor-specific features", "System");
        await _unitOfWork.RoleRepository.AddAsync(vendorRole);

        // Get basic permissions for vendors
        var permissions = await _unitOfWork.PermissionRepository.GetAllAsync();
        foreach (var permission in permissions)
        {
            if (permission.Name.StartsWith("Vendor.") || permission.Name == "Profile.View" || permission.Name == "Profile.Edit")
            {
                vendorRole.AddPermission(permission, "System");
            }
        }

        return vendorRole;
    }

    private string GenerateRandomPassword()
    {
        // Generate a random password with at least one uppercase letter, one lowercase letter,
        // one digit, and one special character
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
        var random = new Random();
        var password = new char[12];

        // Ensure at least one of each required character type
        password[0] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[random.Next(26)]; // Uppercase
        password[1] = "abcdefghijklmnopqrstuvwxyz"[random.Next(26)]; // Lowercase
        password[2] = "0123456789"[random.Next(10)]; // Digit
        password[3] = "!@#$%^&*()"[random.Next(10)]; // Special

        // Fill the rest with random characters
        for (int i = 4; i < password.Length; i++)
        {
            password[i] = chars[random.Next(chars.Length)];
        }

        // Shuffle the password
        for (int i = 0; i < password.Length; i++)
        {
            int swapIndex = random.Next(password.Length);
            char temp = password[i];
            password[i] = password[swapIndex];
            password[swapIndex] = temp;
        }

        return new string(password);
    }

    private async Task SendCredentialsEmail(string email, string businessName, string username, string password)
    {
        var subject = "Your VMS Account Credentials";
        var body = $@"
Dear {businessName},

Your account has been created in the Vendor Management System.

Here are your login credentials:
Username: {username}
Password: {password}

Please change your password after your first login.

Best regards,
VMS Team";

        await _emailService.SendEmailAsync(email, subject, body);
        _logger.LogInformation("Sent credentials email to {Email}", email);
    }

    private async Task PublishUserCreatedEvent(User user)
    {
        var userCreatedEvent = new UserCreatedEvent(
            user.Id,
            user.Email,
            user.PhoneNumber,
            user.EmailVerified,
            user.PhoneNumberVerified);

        await _eventPublisher.PublishAsync(userCreatedEvent);
        _logger.LogInformation("Published UserCreated event for user {UserId}", user.Id);
    }
}
