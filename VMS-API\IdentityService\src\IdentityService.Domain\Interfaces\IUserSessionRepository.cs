using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Interfaces;

/// <summary>
/// Repository for user sessions
/// </summary>
public interface IUserSessionRepository : IGenericRepository<UserSession>
{
    /// <summary>
    /// Gets a session by token
    /// </summary>
    Task<UserSession> GetByTokenAsync(string token);
    
    /// <summary>
    /// Gets a session by refresh token
    /// </summary>
    Task<UserSession> GetByRefreshTokenAsync(string refreshToken);
    
    /// <summary>
    /// Gets all active sessions for a user
    /// </summary>
    Task<IEnumerable<UserSession>> GetActiveSessionsForUserAsync(Guid userId);
    
    /// <summary>
    /// Gets the number of active sessions for a user
    /// </summary>
    Task<int> GetActiveSessionCountForUserAsync(Guid userId);
    
    /// <summary>
    /// Gets all sessions for a user with optional filtering
    /// </summary>
    Task<IEnumerable<UserSession>> GetSessionsForUserAsync(
        Guid userId, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        SessionStatus? status = null, 
        int pageNumber = 1, 
        int pageSize = 10);
    
    /// <summary>
    /// Gets all idle sessions (sessions with no activity for a specified period)
    /// </summary>
    Task<IEnumerable<UserSession>> GetIdleSessionsAsync(int idleTimeoutMinutes);
    
    /// <summary>
    /// Gets all expired sessions (sessions that have passed their expiry time but are still marked as active)
    /// </summary>
    Task<IEnumerable<UserSession>> GetExpiredSessionsAsync();
}
