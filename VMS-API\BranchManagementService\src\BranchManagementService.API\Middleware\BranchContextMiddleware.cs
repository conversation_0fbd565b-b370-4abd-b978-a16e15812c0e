using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Context;

namespace BranchManagementService.API.Middleware
{
    /// <summary>
    /// Middleware for extracting branch context from JWT claims
    /// </summary>
    public class BranchContextMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<BranchContextMiddleware> _logger;

        public BranchContextMiddleware(RequestDelegate next, ILogger<BranchContextMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // Skip middleware for non-API routes or authentication routes
                var path = context.Request.Path.Value?.ToLowerInvariant();
                if (path == null ||
                    !path.StartsWith("/api") ||
                    path.StartsWith("/api/auth") ||
                    path.StartsWith("/swagger"))
                {
                    await _next(context);
                    return;
                }

                // Extract claims from the user
                var user = context.User;
                if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                {
                    _logger.LogWarning("User is null or not authenticated");
                    await _next(context);
                    return;
                }

                // Extract vendor ID
                var vendorIdClaim = user.Claims.FirstOrDefault(c => c.Type == "VendorId");
                if (vendorIdClaim == null || !Guid.TryParse(vendorIdClaim.Value, out var vendorId))
                {
                    _logger.LogWarning("VendorId claim not found or invalid");
                    await _next(context);
                    return;
                }

                // Extract branch ID if present
                Guid? branchId = null;
                var branchIdClaim = user.Claims.FirstOrDefault(c => c.Type == "BranchId");
                if (branchIdClaim != null && Guid.TryParse(branchIdClaim.Value, out var parsedBranchId))
                {
                    branchId = parsedBranchId;
                }

                // Extract roles
                var roles = user.Claims
                    .Where(c => c.Type == ClaimTypes.Role)
                    .Select(c => c.Value)
                    .ToList();

                // Determine if user is vendor admin or branch admin
                bool isVendorAdmin = roles.Contains("VendorAdmin");
                bool isBranchAdmin = roles.Contains("BranchAdmin");

                // Create branch context
                var branchContext = new BranchContext
                {
                    VendorId = vendorId,
                    BranchId = branchId,
                    IsVendorAdmin = isVendorAdmin,
                    IsBranchAdmin = isBranchAdmin
                };

                // Add branch context to HttpContext.Items
                context.Items["BranchContext"] = branchContext;

                _logger.LogInformation(
                    "Branch context set: VendorId={VendorId}, BranchId={BranchId}, IsVendorAdmin={IsVendorAdmin}, IsBranchAdmin={IsBranchAdmin}",
                    vendorId, branchId, isVendorAdmin, isBranchAdmin);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in BranchContextMiddleware");
            }

            // Continue with the pipeline
            await _next(context);
        }
    }
}
