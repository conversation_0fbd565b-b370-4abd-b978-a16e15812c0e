using Microsoft.Extensions.Logging;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.Infrastructure.Services
{
    public class BillingService : IBillingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly INotificationService _notificationService;
        private readonly IPromotionService _promotionService;
        private readonly ILogger<BillingService> _logger;

        public BillingService(
            IUnitOfWork unitOfWork,
            INotificationService notificationService,
            IPromotionService promotionService,
            ILogger<BillingService> logger)
        {
            _unitOfWork = unitOfWork;
            _notificationService = notificationService;
            _promotionService = promotionService;
            _logger = logger;
        }

        public async Task<Invoice> GenerateInvoiceAsync(
            Guid subscriptionId,
            decimal amount,
            decimal taxAmount,
            decimal discountAmount,
            string? notes = null)
        {
            _logger.LogInformation("Generating invoice for subscription {SubscriptionId}", subscriptionId);

            // Get the subscription
            var subscription = await _unitOfWork.Subscriptions.GetByIdAsync(subscriptionId);
            if (subscription == null)
            {
                throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
            }

            // Generate invoice number
            var invoiceNumber = await _unitOfWork.Invoices.GenerateInvoiceNumberAsync();

            // Create the invoice
            var dueDate = DateTime.UtcNow.AddDays(15); // 15 days due date
            var billingPeriod = MapBillingCycleToPeriod(subscription.BillingCycle);

            var invoice = new Invoice(
                subscriptionId,
                invoiceNumber,
                DateTime.UtcNow,
                dueDate,
                amount,
                taxAmount,
                discountAmount,
                billingPeriod,
                notes);

            // Save the invoice
            await _unitOfWork.Invoices.AddAsync(invoice);
            await _unitOfWork.SaveChangesAsync();

            // Update the subscription's last billing date
            subscription.ProcessBilling();
            await _unitOfWork.SaveChangesAsync();

            // Send notification
            await _notificationService.SendInvoiceCreatedAsync(invoice, subscription);

            return invoice;
        }

        public async Task<Invoice> ProcessPaymentAsync(string invoiceNumber, string paymentReference)
        {
            _logger.LogInformation("Processing payment for invoice {InvoiceNumber}", invoiceNumber);

            // Get the invoice
            var invoice = await _unitOfWork.Invoices.GetInvoiceByNumberAsync(invoiceNumber);
            if (invoice == null)
            {
                throw new ArgumentException($"Invoice with number {invoiceNumber} not found");
            }

            // Mark the invoice as paid
            invoice.MarkAsPaid(paymentReference, DateTime.UtcNow);
            await _unitOfWork.SaveChangesAsync();

            // Get the subscription for notification
            var subscription = await _unitOfWork.Subscriptions.GetByIdAsync(invoice.SubscriptionId);
            if (subscription != null)
            {
                // Send notification
                await _notificationService.SendPaymentReceivedAsync(invoice, subscription);
            }

            return invoice;
        }

        public async Task<IReadOnlyList<Invoice>> GetInvoiceHistoryAsync(Guid subscriptionId)
        {
            return await _unitOfWork.Invoices.GetInvoicesBySubscriptionIdAsync(subscriptionId);
        }

        public async Task<IReadOnlyList<Invoice>> GetOverdueInvoicesAsync()
        {
            return await _unitOfWork.Invoices.GetOverdueInvoicesAsync();
        }

        public async Task<Invoice> ApplyPromotionCodeAsync(string invoiceNumber, string promotionCode)
        {
            _logger.LogInformation("Applying promotion code {PromotionCode} to invoice {InvoiceNumber}",
                promotionCode, invoiceNumber);

            // Get the invoice
            var invoice = await _unitOfWork.Invoices.GetInvoiceByNumberAsync(invoiceNumber);
            if (invoice == null)
            {
                throw new ArgumentException($"Invoice with number {invoiceNumber} not found");
            }

            // Validate the promotion code
            if (!await _promotionService.ValidatePromotionCodeAsync(promotionCode))
            {
                throw new ArgumentException($"Promotion code {promotionCode} is invalid");
            }

            // Calculate the discount
            var discount = await _promotionService.CalculateDiscountAsync(promotionCode, invoice.Amount);

            // Apply the promotion code to the invoice
            invoice.ApplyPromotionCode(promotionCode, discount);
            await _unitOfWork.SaveChangesAsync();

            // Use the promotion code (increment usage count)
            await _promotionService.UsePromotionCodeAsync(promotionCode);

            return invoice;
        }

        public async Task ProcessDueInvoicesAsync()
        {
            _logger.LogInformation("Processing due invoices");

            // Get all overdue invoices
            var overdueInvoices = await GetOverdueInvoicesAsync();

            foreach (var invoice in overdueInvoices)
            {
                // Get the subscription
                var subscription = await _unitOfWork.Subscriptions.GetByIdAsync(invoice.SubscriptionId);
                if (subscription == null)
                {
                    _logger.LogWarning("Subscription with ID {SubscriptionId} not found for invoice {InvoiceNumber}",
                        invoice.SubscriptionId, invoice.InvoiceNumber);
                    continue;
                }

                // Send overdue notification
                await _notificationService.SendPaymentOverdueAsync(invoice, subscription);

                // If the invoice is more than 30 days overdue, put the subscription in grace period
                var daysOverdue = (DateTime.UtcNow - invoice.DueDate).Days;
                if (daysOverdue > 30 && subscription.Status == SubscriptionStatus.Active)
                {
                    _logger.LogInformation("Putting subscription {SubscriptionId} in grace period due to overdue invoice",
                        subscription.Id);

                    subscription.EnterGracePeriod();
                    await _unitOfWork.SaveChangesAsync();
                }

                // If the invoice is more than 45 days overdue, soft lock the subscription
                if (daysOverdue > 45 && subscription.Status == SubscriptionStatus.GracePeriod)
                {
                    _logger.LogInformation("Soft locking subscription {SubscriptionId} due to overdue invoice",
                        subscription.Id);

                    subscription.SoftLock();
                    await _unitOfWork.SaveChangesAsync();
                }

                // If the invoice is more than 60 days overdue, hard lock the subscription
                if (daysOverdue > 60 && subscription.Status == SubscriptionStatus.SoftLocked)
                {
                    _logger.LogInformation("Hard locking subscription {SubscriptionId} due to overdue invoice",
                        subscription.Id);

                    subscription.HardLock();
                    await _unitOfWork.SaveChangesAsync();
                }
            }
        }

        private BillingPeriod MapBillingCycleToPeriod(BillingCycle cycle)
        {
            return cycle switch
            {
                BillingCycle.Monthly => BillingPeriod.Monthly,
                BillingCycle.Quarterly => BillingPeriod.Quarterly,
                BillingCycle.Yearly => BillingPeriod.Yearly,
                _ => BillingPeriod.Monthly
            };
        }
    }
}
