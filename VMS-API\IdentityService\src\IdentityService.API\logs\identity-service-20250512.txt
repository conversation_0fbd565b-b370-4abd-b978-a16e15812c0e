2025-05-12 13:17:36.011 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-12 13:17:36.452 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-12 13:17:37.131 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-12 13:17:37.163 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-12 13:17:37.167 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-12 13:17:37.172 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-12 13:17:39.189 +05:30 [INF] Executed DbCommand (215ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-12 13:17:40.685 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-12 13:17:40.959 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-12 13:17:41.153 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-12 13:17:41.176 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-12 13:17:41.207 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-12 13:17:41.274 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-12 13:17:41.922 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-12 13:17:42.091 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-12 13:17:42.470 +05:30 [DBG] Starting bus instances: IBus
2025-05-12 13:17:42.537 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-12 13:17:42.583 +05:30 [INF] Session cleanup service is starting
2025-05-12 13:17:42.595 +05:30 [DBG] Starting session cleanup
2025-05-12 13:17:42.676 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-12 13:17:43.439 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 61991)
2025-05-12 13:17:43.901 +05:30 [INF] Executed DbCommand (48ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:17:44.025 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_jysyyyfpkikqdqp3bdq3nkkgdr?temporary=true"
2025-05-12 13:17:44.060 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-12 13:17:44.155 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-12 13:17:44.157 +05:30 [INF] Hosting environment: Development
2025-05-12 13:17:44.159 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-12 13:17:44.097 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-12 13:17:44.198 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-12 13:17:44.206 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-12 13:17:44.231 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:17:44.242 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-12 13:17:44.256 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-12 13:17:44.277 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-12 13:17:44.296 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:17:44.549 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-12 13:17:44.560 +05:30 [DBG] Session cleanup completed
2025-05-12 13:17:44.567 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-12 13:17:44.598 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-12 13:17:44.598 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-12 13:17:44.725 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-RnhiQNBOE5uEF0bY6F1BGg
2025-05-12 13:17:44.725 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-vCWBlF3DLehWQ5fN-pjzSQ
2025-05-12 13:17:44.730 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-12 13:17:44.732 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-12 13:17:44.741 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-12 13:18:05.973 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-12 13:18:06.106 +05:30 [INF] Request was redirected to /swagger
2025-05-12 13:18:06.113 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 192.4109ms
2025-05-12 13:18:06.125 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-12 13:18:06.244 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 118.2029ms
2025-05-12 13:18:07.294 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-12 13:18:07.837 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 543.456ms
2025-05-12 13:19:14.510 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-12 13:19:14.515 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 4.6985ms
2025-05-12 13:19:18.824 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/test/token - null null
2025-05-12 13:19:18.834 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-12 13:19:18.867 +05:30 [INF] Token received and is being processed
2025-05-12 13:19:18.971 +05:30 [WRN] User is null or not authenticated
2025-05-12 13:19:18.973 +05:30 [WRN] No user ID found in token
2025-05-12 13:19:18.976 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:19:18.994 +05:30 [INF] Route matched with {action = "GenerateTestToken", controller = "Test"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GenerateTestToken() on controller IdentityService.API.Controllers.TestController (IdentityService.API).
2025-05-12 13:19:19.057 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-12 13:19:19.076 +05:30 [INF] Executed action IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API) in 67.8331ms
2025-05-12 13:19:19.079 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:19:19.084 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/test/token - 200 null application/json; charset=utf-8 260.1704ms
2025-05-12 13:19:19.540 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json 232
2025-05-12 13:19:19.552 +05:30 [INF] Token received and is being processed
2025-05-12 13:19:19.614 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 7c118016-1e2d-466a-ba6d-fe41a1126953, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 7c118016-1e2d-466a-ba6d-fe41a1126953, jti: 8a6f0721-3e71-4d03-8cdc-101e7e718fb5, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: 1747036159, exp: 1747039759, iss: identity-service, aud: vms-api
2025-05-12 13:19:19.618 +05:30 [INF] NameIdentifier claim found: 7c118016-1e2d-466a-ba6d-fe41a1126953
2025-05-12 13:19:19.678 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-12 13:19:19.688 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.R5SiQnecSu-H9x0pOa2lR7Te8r4-1Nz8PsGtY3EhJ8Y not found
2025-05-12 13:19:19.692 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.R5SiQnecSu-H9x0pOa2lR7Te8r4-1Nz8PsGtY3EhJ8Y
2025-05-12 13:19:19.703 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 401 null application/json; charset=utf-8 163.3256ms
2025-05-12 13:19:46.372 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-12 13:19:46.379 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 7.1532ms
2025-05-12 13:20:25.573 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-12 13:20:25.577 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 4.3628ms
2025-05-12 13:20:29.727 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/test/token - null null
2025-05-12 13:20:29.735 +05:30 [INF] Token received and is being processed
2025-05-12 13:20:29.741 +05:30 [WRN] User is null or not authenticated
2025-05-12 13:20:29.743 +05:30 [WRN] No user ID found in token
2025-05-12 13:20:29.744 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:20:29.751 +05:30 [INF] Route matched with {action = "GenerateTestToken", controller = "Test"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GenerateTestToken() on controller IdentityService.API.Controllers.TestController (IdentityService.API).
2025-05-12 13:20:29.756 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-12 13:20:29.760 +05:30 [INF] Executed action IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API) in 4.9626ms
2025-05-12 13:20:29.765 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:20:29.770 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/test/token - 200 null application/json; charset=utf-8 42.3005ms
2025-05-12 13:22:44.573 +05:30 [DBG] Starting session cleanup
2025-05-12 13:22:44.691 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:22:44.700 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:22:44.711 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:22:44.712 +05:30 [DBG] Session cleanup completed
2025-05-12 13:27:44.718 +05:30 [DBG] Starting session cleanup
2025-05-12 13:27:44.724 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:27:44.733 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:27:44.747 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:27:44.748 +05:30 [DBG] Session cleanup completed
2025-05-12 13:30:20.874 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-12 13:30:20.972 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 97.1096ms
2025-05-12 13:30:25.177 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/test/token - null null
2025-05-12 13:30:25.180 +05:30 [INF] Token received and is being processed
2025-05-12 13:30:25.183 +05:30 [WRN] User is null or not authenticated
2025-05-12 13:30:25.184 +05:30 [WRN] No user ID found in token
2025-05-12 13:30:25.187 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:30:25.190 +05:30 [INF] Route matched with {action = "GenerateTestToken", controller = "Test"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GenerateTestToken() on controller IdentityService.API.Controllers.TestController (IdentityService.API).
2025-05-12 13:30:25.195 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-12 13:30:25.202 +05:30 [INF] Executed action IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API) in 8.6788ms
2025-05-12 13:30:25.206 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:30:25.209 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/test/token - 200 null application/json; charset=utf-8 32.3334ms
2025-05-12 13:30:25.398 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/health - null null
2025-05-12 13:30:25.402 +05:30 [INF] Token received and is being processed
2025-05-12 13:30:25.405 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, jti: 5d87a75d-1ab5-4d5f-ab50-66efe88f58b7, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:30:25.408 +05:30 [INF] NameIdentifier claim found: a2fbb078-b242-4b79-81d5-c244efcc960c
2025-05-12 13:30:25.412 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, jti: 5d87a75d-1ab5-4d5f-ab50-66efe88f58b7, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:30:25.420 +05:30 [INF] Successfully extracted user ID: "a2fbb078-b242-4b79-81d5-c244efcc960c"
2025-05-12 13:30:25.422 +05:30 [INF] User ID from token: "a2fbb078-b242-4b79-81d5-c244efcc960c"
2025-05-12 13:30:25.424 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/health - 404 0 null 25.6328ms
2025-05-12 13:30:25.429 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5263/health, Response status code: 404
2025-05-12 13:30:25.606 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/users - null null
2025-05-12 13:30:25.609 +05:30 [INF] Token received and is being processed
2025-05-12 13:30:25.611 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, jti: 5d87a75d-1ab5-4d5f-ab50-66efe88f58b7, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:30:25.614 +05:30 [INF] NameIdentifier claim found: a2fbb078-b242-4b79-81d5-c244efcc960c
2025-05-12 13:30:25.626 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Users.View)
2025-05-12 13:30:25.631 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-12 13:30:25.635 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/users - 403 0 null 28.9096ms
2025-05-12 13:30:25.654 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/roles - null null
2025-05-12 13:30:25.657 +05:30 [INF] Token received and is being processed
2025-05-12 13:30:25.659 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, jti: 5d87a75d-1ab5-4d5f-ab50-66efe88f58b7, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:30:25.662 +05:30 [INF] NameIdentifier claim found: a2fbb078-b242-4b79-81d5-c244efcc960c
2025-05-12 13:30:25.667 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Roles.View)
2025-05-12 13:30:25.674 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-12 13:30:25.675 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/roles - 403 0 null 20.3633ms
2025-05-12 13:30:25.780 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json 232
2025-05-12 13:30:25.783 +05:30 [INF] Token received and is being processed
2025-05-12 13:30:25.786 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: a2fbb078-b242-4b79-81d5-c244efcc960c, jti: 5d87a75d-1ab5-4d5f-ab50-66efe88f58b7, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:30:25.789 +05:30 [INF] NameIdentifier claim found: a2fbb078-b242-4b79-81d5-c244efcc960c
2025-05-12 13:30:25.904 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-12 13:30:25.910 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2uHllXfBW3WxFncvASiJAXWQ8dUxlf4SKy8gKZiDu6g not found
2025-05-12 13:30:25.917 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2uHllXfBW3WxFncvASiJAXWQ8dUxlf4SKy8gKZiDu6g
2025-05-12 13:30:25.923 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 401 null application/json; charset=utf-8 142.631ms
2025-05-12 13:32:44.738 +05:30 [DBG] Starting session cleanup
2025-05-12 13:32:44.745 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:32:44.768 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:32:44.776 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:32:44.778 +05:30 [DBG] Session cleanup completed
2025-05-12 13:36:25.501 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-12 13:36:25.506 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 5.1822ms
2025-05-12 13:36:29.725 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/test/token - null null
2025-05-12 13:36:29.728 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:29.731 +05:30 [WRN] User is null or not authenticated
2025-05-12 13:36:29.731 +05:30 [WRN] No user ID found in token
2025-05-12 13:36:29.733 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:36:29.737 +05:30 [INF] Route matched with {action = "GenerateTestToken", controller = "Test"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GenerateTestToken() on controller IdentityService.API.Controllers.TestController (IdentityService.API).
2025-05-12 13:36:29.743 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-12 13:36:29.746 +05:30 [INF] Executed action IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API) in 4.11ms
2025-05-12 13:36:29.750 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-12 13:36:29.755 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/test/token - 200 null application/json; charset=utf-8 29.3936ms
2025-05-12 13:36:30.040 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/health - null null
2025-05-12 13:36:30.044 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:30.046 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:30.049 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:30.052 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:30.058 +05:30 [INF] Successfully extracted user ID: "069e94fc-a928-4af4-8e92-dc69a8d3950d"
2025-05-12 13:36:30.060 +05:30 [INF] User ID from token: "069e94fc-a928-4af4-8e92-dc69a8d3950d"
2025-05-12 13:36:30.062 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/health - 404 0 null 21.421ms
2025-05-12 13:36:30.070 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5263/health, Response status code: 404
2025-05-12 13:36:30.261 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/test/token - null null
2025-05-12 13:36:30.264 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:30.266 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:30.271 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:30.464 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-12 13:36:30.471 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc not found
2025-05-12 13:36:30.476 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc
2025-05-12 13:36:30.480 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/test/token - 401 null application/json; charset=utf-8 219.114ms
2025-05-12 13:36:30.584 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/auth/login - application/json 70
2025-05-12 13:36:30.587 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:30.589 +05:30 [WRN] User is null or not authenticated
2025-05-12 13:36:30.591 +05:30 [WRN] No user ID found in token
2025-05-12 13:36:30.593 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-12 13:36:30.605 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-12 13:36:30.688 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-12 13:36:30.689 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-12 13:36:30.692 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-12 13:36:30.696 +05:30 [INF] Validating request of type LoginCommand
2025-05-12 13:36:30.703 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": null,
    "Otp": null,
    "RememberMe": false,
    "IsEmailLogin": true,
    "IsPhoneLogin": false
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-12 13:36:30.707 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-12 13:36:30.708 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-12 13:36:30.726 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-12 13:36:30.800 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-12 13:36:30.836 +05:30 [INF] Executed DbCommand (20ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-12 13:36:30.849 +05:30 [WRN] Login failed: Invalid email or password
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 67
2025-05-12 13:36:31.097 +05:30 [INF] Executed DbCommand (19ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
2025-05-12 13:36:31.127 +05:30 [WRN] Login failed for user
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 67
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 216
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.AuthController.Login(LoginRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\AuthController.cs:line 31
2025-05-12 13:36:31.154 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-12 13:36:31.161 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 551.7098ms
2025-05-12 13:36:31.163 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-12 13:36:31.165 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/auth/login - 401 null application/json; charset=utf-8 581.5987ms
2025-05-12 13:36:31.178 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/users - null null
2025-05-12 13:36:31.181 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:31.183 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:31.189 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:31.197 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Users.View)
2025-05-12 13:36:31.199 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-12 13:36:31.201 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/users - 403 0 null 22.6739ms
2025-05-12 13:36:31.232 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json 232
2025-05-12 13:36:31.235 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:31.236 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:31.244 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:31.249 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-12 13:36:31.261 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc not found
2025-05-12 13:36:31.266 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc
2025-05-12 13:36:31.270 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 401 null application/json; charset=utf-8 38.8228ms
2025-05-12 13:36:31.300 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/roles - null null
2025-05-12 13:36:31.309 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:31.313 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:31.317 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:31.319 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Roles.View)
2025-05-12 13:36:31.322 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-12 13:36:31.324 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/roles - 403 0 null 23.4298ms
2025-05-12 13:36:31.395 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/roles - application/json 209
2025-05-12 13:36:31.398 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:31.400 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:31.405 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:31.412 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-12 13:36:31.419 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc not found
2025-05-12 13:36:31.430 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc
2025-05-12 13:36:31.435 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/roles - 401 null application/json; charset=utf-8 40.0404ms
2025-05-12 13:36:31.449 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/ - null null
2025-05-12 13:36:31.452 +05:30 [INF] Token received and is being processed
2025-05-12 13:36:31.454 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 069e94fc-a928-4af4-8e92-dc69a8d3950d, jti: 4f37db1d-748a-4196-a850-748ba08f9fde, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: **********, exp: **********, iss: identity-service, aud: vms-api
2025-05-12 13:36:31.462 +05:30 [INF] NameIdentifier claim found: 069e94fc-a928-4af4-8e92-dc69a8d3950d
2025-05-12 13:36:31.467 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-12 13:36:31.477 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc not found
2025-05-12 13:36:31.481 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SkJHXg78z2yTpeCpvFhJ5Xcr_5TI8S0Xw99W9HmakQc
2025-05-12 13:36:31.484 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/ - 401 null application/json; charset=utf-8 35.2855ms
2025-05-12 13:37:44.788 +05:30 [DBG] Starting session cleanup
2025-05-12 13:37:44.794 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:37:44.805 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:37:44.818 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:37:44.821 +05:30 [DBG] Session cleanup completed
2025-05-12 13:42:44.821 +05:30 [DBG] Starting session cleanup
2025-05-12 13:42:44.911 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:42:44.919 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:42:44.930 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:42:44.931 +05:30 [DBG] Session cleanup completed
2025-05-12 13:47:44.932 +05:30 [DBG] Starting session cleanup
2025-05-12 13:47:44.936 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:47:44.954 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:47:44.963 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:47:44.967 +05:30 [DBG] Session cleanup completed
2025-05-12 13:52:44.979 +05:30 [DBG] Starting session cleanup
2025-05-12 13:52:45.092 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:52:45.101 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:52:45.113 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:52:45.115 +05:30 [DBG] Session cleanup completed
2025-05-12 13:57:45.117 +05:30 [DBG] Starting session cleanup
2025-05-12 13:57:45.120 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 13:57:45.144 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 13:57:45.155 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 13:57:45.157 +05:30 [DBG] Session cleanup completed
2025-05-12 14:02:45.162 +05:30 [DBG] Starting session cleanup
2025-05-12 14:02:45.265 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 14:02:45.274 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 14:02:45.281 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 14:02:45.283 +05:30 [DBG] Session cleanup completed
2025-05-12 14:07:45.294 +05:30 [DBG] Starting session cleanup
2025-05-12 14:07:45.297 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 14:07:45.321 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 14:07:45.331 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 14:07:45.333 +05:30 [DBG] Session cleanup completed
2025-05-12 14:12:45.348 +05:30 [DBG] Starting session cleanup
2025-05-12 14:12:45.544 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 14:12:45.553 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 14:12:45.562 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 14:12:45.564 +05:30 [DBG] Session cleanup completed
2025-05-12 14:17:45.569 +05:30 [DBG] Starting session cleanup
2025-05-12 14:17:45.573 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 14:17:45.599 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 14:17:45.608 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 14:17:45.610 +05:30 [DBG] Session cleanup completed
2025-05-12 14:22:45.618 +05:30 [DBG] Starting session cleanup
2025-05-12 14:22:45.813 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 14:22:45.834 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 14:22:45.840 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 14:22:45.842 +05:30 [DBG] Session cleanup completed
2025-05-12 15:17:59.379 +05:30 [DBG] Starting session cleanup
2025-05-12 15:17:59.384 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 15:17:59.391 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 15:17:59.400 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 15:17:59.402 +05:30 [DBG] Session cleanup completed
2025-05-12 15:22:59.418 +05:30 [DBG] Starting session cleanup
2025-05-12 15:22:59.560 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 15:22:59.571 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 15:22:59.584 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 15:22:59.585 +05:30 [DBG] Session cleanup completed
2025-05-12 15:27:59.601 +05:30 [DBG] Starting session cleanup
2025-05-12 15:27:59.606 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-12 15:27:59.614 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-12 15:27:59.624 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-12 15:27:59.626 +05:30 [DBG] Session cleanup completed
