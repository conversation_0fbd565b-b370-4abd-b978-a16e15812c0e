using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BranchManagementService.Infrastructure.Messaging.Publishers
{
    public class EventPublisher : IEventPublisher
    {
        private readonly IPublishEndpoint _publishEndpoint;
        private readonly ILogger<EventPublisher> _logger;

        public EventPublisher(IPublishEndpoint publishEndpoint, ILogger<EventPublisher> logger)
        {
            _publishEndpoint = publishEndpoint;
            _logger = logger;
        }

        public async Task PublishAsync<T>(T @event) where T : class
        {
            try
            {
                _logger.LogInformation("Publishing event {EventType}", typeof(T).Name);
                await _publishEndpoint.Publish(@event);
                _logger.LogInformation("Event {EventType} published successfully", typeof(T).Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing event {EventType}", typeof(T).Name);
                throw;
            }
        }
    }

    public interface IEventPublisher
    {
        Task PublishAsync<T>(T @event) where T : class;
    }
}
