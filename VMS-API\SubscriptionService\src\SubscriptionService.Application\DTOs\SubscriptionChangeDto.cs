using SubscriptionService.Domain.Enums;
using System;

namespace SubscriptionService.Application.DTOs;

public class SubscriptionChangeDto
{
    public Guid Id { get; set; }
    public Guid SubscriptionId { get; set; }
    public ChangeType ChangeType { get; set; }
    public Guid OldPlanId { get; set; }
    public Guid NewPlanId { get; set; }
    public DateTime EffectiveDate { get; set; }
    public string? Notes { get; set; }
    public string? PromotionCode { get; set; }
    public decimal PriceAdjustment { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}
