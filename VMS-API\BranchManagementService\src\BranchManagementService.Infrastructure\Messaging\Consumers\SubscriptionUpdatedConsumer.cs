using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using VMS.Contracts.Subscription.Events;

namespace BranchManagementService.Infrastructure.Messaging.Consumers
{
    public class SubscriptionUpdatedConsumer : IConsumer<SubscriptionUpdated>
    {
        private readonly ILogger<SubscriptionUpdatedConsumer> _logger;

        public SubscriptionUpdatedConsumer(ILogger<SubscriptionUpdatedConsumer> logger)
        {
            _logger = logger;
        }

        public Task Consume(ConsumeContext<SubscriptionUpdated> context)
        {
            try
            {
                var @event = context.Message;
                _logger.LogInformation("Received SubscriptionUpdated event for vendor {VendorId}", @event.VendorId);

                // TODO: Implement logic to handle subscription update
                // This could include updating branch limits for the vendor

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error consuming SubscriptionUpdated event");
                throw;
            }
        }
    }
}
