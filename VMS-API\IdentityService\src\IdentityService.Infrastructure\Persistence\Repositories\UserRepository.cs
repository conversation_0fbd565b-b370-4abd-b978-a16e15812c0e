using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Infrastructure.Persistence.Repositories;

public class UserRepository : GenericRepository<User>, IUserRepository
{
    public UserRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                    .ThenInclude(r => r.RolePermissions)
                        .ThenInclude(rp => rp.Permission)
            .Include(u => u.PasswordResetTokens)
            .FirstOrDefaultAsync(u => u.Email == email);
    }

    public async Task<User?> GetByPhoneNumberAsync(string phoneNumber)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                    .ThenInclude(r => r.RolePermissions)
                        .ThenInclude(rp => rp.Permission)
            .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);
    }

    public async Task<User?> GetByIdWithRolesAsync(Guid userId)
    {
        // Get user with explicit loading of UserRoles to ensure we have the complete entity
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .AsNoTracking() // First get without tracking to avoid stale data
            .FirstOrDefaultAsync(u => u.Id == userId)
            .ContinueWith(async task =>
            {
                var user = await task;
                if (user == null) return null;

                // Then get a fresh tracked instance
                return await _dbSet
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == userId);
            })
            .Unwrap();
    }

    public async Task<List<string>> GetUserRolesAsync(Guid userId)
    {
        return await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Select(ur => ur.Role.Name)
            .ToListAsync();
    }

    // This method is used internally by the repository
    private async Task<List<Role>> GetUserRoleEntitiesAsync(Guid userId)
    {
        return await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Select(ur => ur.Role)
            .ToListAsync();
    }

    public async Task<List<Permission>> GetUserPermissionsAsync(Guid userId)
    {
        var roles = await GetUserRoleEntitiesAsync(userId);
        var permissions = new List<Permission>();

        foreach (var role in roles)
        {
            var rolePermissions = await _context.RolePermissions
                .Where(rp => rp.RoleId == role.Id)
                .Select(rp => rp.Permission)
                .ToListAsync();

            permissions.AddRange(rolePermissions);
        }

        return permissions.Distinct().ToList();
    }

    public async Task<bool> IsInRoleAsync(User user, string roleName)
    {
        return await _context.UserRoles
            .AnyAsync(ur => ur.UserId == user.Id && ur.Role.Name == roleName);
    }

    public async Task<bool> HasPermissionAsync(User user, string permissionName)
    {
        var roles = await GetUserRoleEntitiesAsync(user.Id);
        foreach (var role in roles)
        {
            var hasPermission = await _context.RolePermissions
                .AnyAsync(rp => rp.RoleId == role.Id && rp.Permission.Name == permissionName);

            if (hasPermission)
                return true;
        }
        return false;
    }

    public async Task<bool> ExistsByEmailAsync(string email)
    {
        return await _dbSet.AnyAsync(u => u.Email == email);
    }

    public async Task<bool> ExistsByPhoneNumberAsync(string phoneNumber)
    {
        return await _dbSet.AnyAsync(u => u.PhoneNumber == phoneNumber);
    }

    public async Task<IReadOnlyList<User>> GetUsersByRoleAsync(string roleName)
    {
        return await _context.UserRoles
            .Where(ur => ur.Role.Name == roleName)
            .Select(ur => ur.User)
            .ToListAsync();
    }

    public async Task UpdatePasswordAsync(Guid userId, string passwordHash)
    {
        var user = await GetByIdAsync(userId);
        if (user != null)
        {
            user.UpdatePassword(passwordHash, "System");
            await _context.SaveChangesAsync();
        }
    }

    public async Task UpdateLastLoginAsync(Guid userId)
    {
        var user = await GetByIdAsync(userId);
        if (user != null)
        {
            user.UpdateLastLogin();
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> ValidateRefreshTokenAsync(Guid userId, string refreshToken)
    {
        var user = await GetByIdAsync(userId);
        return user?.RefreshToken == refreshToken;
    }

    public async Task<UserSubscription?> GetActiveSubscriptionAsync(Guid userId)
    {
        var user = await _dbSet
            .Include(u => u.Subscriptions)
                .ThenInclude(s => s.Features)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
            return null;

        return user.Subscriptions
            .Where(s => s.IsActive())
            .OrderByDescending(s => s.Tier)
            .FirstOrDefault();
    }

    public async Task<List<UserSubscription>> GetUserSubscriptionsAsync(Guid userId)
    {
        var user = await _dbSet
            .Include(u => u.Subscriptions)
                .ThenInclude(s => s.Features)
            .FirstOrDefaultAsync(u => u.Id == userId);

        return user?.Subscriptions.ToList() ?? new List<UserSubscription>();
    }

    public async Task<bool> HasActiveSubscriptionAsync(Guid userId)
    {
        var user = await _dbSet
            .Include(u => u.Subscriptions)
            .FirstOrDefaultAsync(u => u.Id == userId);

        return user?.HasActiveSubscription() ?? false;
    }

    public async Task<SubscriptionTier> GetUserSubscriptionTierAsync(Guid userId)
    {
        var user = await _dbSet
            .Include(u => u.Subscriptions)
            .FirstOrDefaultAsync(u => u.Id == userId);

        return user?.GetHighestSubscriptionTier() ?? SubscriptionTier.Free;
    }

    public async Task<bool> HasSubscriptionFeatureAsync(Guid userId, string featureName)
    {
        var user = await _dbSet
            .Include(u => u.Subscriptions)
                .ThenInclude(s => s.Features)
            .FirstOrDefaultAsync(u => u.Id == userId);

        return user?.HasSubscriptionFeature(featureName) ?? false;
    }

    public async Task<List<User>> GetByIdsAsync(List<Guid> userIds)
    {
        return await _dbSet
            .Where(u => userIds.Contains(u.Id))
            .ToListAsync();
    }

    public async Task<bool> UserHasRoleAsync(Guid userId, string roleName)
    {
        return await _context.UserRoles
            .AnyAsync(ur => ur.UserId == userId && ur.Role.Name == roleName);
    }
}