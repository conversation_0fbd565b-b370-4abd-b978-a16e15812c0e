using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Queries;

public class GetUserLoginHistoryQuery : BaseRequest<List<UserLoginHistoryResponse>>
{
    public new Guid? UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public bool? IsSuccessful { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}

public class GetUserLoginHistoryQueryHandler : IRequestHandler<GetUserLoginHistoryQuery, List<UserLoginHistoryResponse>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GetUserLoginHistoryQueryHandler> _logger;
    private readonly ICurrentUserService _currentUserService;

    public GetUserLoginHistoryQueryHandler(
        IUnitOfWork unitOfWork,
        ILogger<GetUserLoginHistoryQueryHandler> logger,
        ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _currentUserService = currentUserService;
    }

    public async Task<List<UserLoginHistoryResponse>> Handle(GetUserLoginHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // If no user ID is provided, use the one from the current user
            var userId = request.UserId ?? _currentUserService.UserId;
            if (userId == null || userId == Guid.Empty)
            {
                throw new InvalidOperationException("User ID is required");
            }

            // Check if the user exists
            var user = await _unitOfWork.UserRepository.GetByIdAsync(userId.Value);
            if (user == null)
            {
                throw new InvalidOperationException($"User with ID {userId} not found");
            }

            // Get all login history entries for the user
            var allLoginHistoryEntries = await _unitOfWork.UserLoginHistoryRepository.GetAllAsync();
            var loginHistoryEntries = allLoginHistoryEntries.ToList();

            // Filter by user ID
            loginHistoryEntries = loginHistoryEntries.Where(h => h.UserId == userId).ToList();

            // Apply additional filters
            if (request.FromDate.HasValue)
            {
                loginHistoryEntries = loginHistoryEntries.Where(h => h.CreatedAt >= request.FromDate.Value).ToList();
            }

            if (request.ToDate.HasValue)
            {
                loginHistoryEntries = loginHistoryEntries.Where(h => h.CreatedAt <= request.ToDate.Value).ToList();
            }

            if (request.IsSuccessful.HasValue)
            {
                loginHistoryEntries = loginHistoryEntries.Where(h => h.IsSuccessful == request.IsSuccessful.Value).ToList();
            }

            // Order by creation date (newest first)
            loginHistoryEntries = loginHistoryEntries.OrderByDescending(h => h.CreatedAt).ToList();

            // Apply pagination
            loginHistoryEntries = loginHistoryEntries
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            // Map to response DTOs
            var response = loginHistoryEntries.Select(h => new UserLoginHistoryResponse
            {
                Id = h.Id,
                UserId = h.UserId,
                Email = h.Email,
                PhoneNumber = h.PhoneNumber ?? string.Empty,
                IsSuccessful = h.IsSuccessful,
                FailureReason = h.FailureReason ?? string.Empty,
                IpAddress = h.IpAddress,
                UserAgent = h.UserAgent,
                DeviceInfo = h.DeviceInfo,
                Location = h.Location,
                LoginMethod = h.LoginMethod.ToString(),
                LoginTime = h.CreatedAt
            }).ToList();

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user login history");
            throw;
        }
    }
}
