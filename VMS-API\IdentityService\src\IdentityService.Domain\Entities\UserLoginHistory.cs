using System;
using IdentityService.Domain.Common;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Entities;

/// <summary>
/// Represents a user login attempt
/// </summary>
public class UserLoginHistory : BaseEntity
{
    /// <summary>
    /// The ID of the user who attempted to log in
    /// </summary>
    public Guid? UserId { get; private set; }
    
    /// <summary>
    /// Reference to the user (may be null for failed logins with non-existent users)
    /// </summary>
    public User User { get; private set; }
    
    /// <summary>
    /// The email used in the login attempt
    /// </summary>
    public string Email { get; private set; }
    
    /// <summary>
    /// The phone number used in the login attempt (if applicable)
    /// </summary>
    public string PhoneNumber { get; private set; }
    
    /// <summary>
    /// Whether the login was successful
    /// </summary>
    public bool IsSuccessful { get; private set; }
    
    /// <summary>
    /// The failure reason (if applicable)
    /// </summary>
    public string FailureReason { get; private set; }
    
    /// <summary>
    /// The IP address of the client
    /// </summary>
    public string IpAddress { get; private set; }
    
    /// <summary>
    /// The user agent of the client
    /// </summary>
    public string UserAgent { get; private set; }
    
    /// <summary>
    /// The device information of the client
    /// </summary>
    public string DeviceInfo { get; private set; }
    
    /// <summary>
    /// The location information of the client (if available)
    /// </summary>
    public string Location { get; private set; }
    
    /// <summary>
    /// The login method used
    /// </summary>
    public LoginMethod LoginMethod { get; private set; }

    // Private constructor for EF Core
    private UserLoginHistory()
    {
        Email = string.Empty;
        PhoneNumber = string.Empty;
        FailureReason = string.Empty;
        IpAddress = string.Empty;
        UserAgent = string.Empty;
        DeviceInfo = string.Empty;
        Location = string.Empty;
    }

    /// <summary>
    /// Creates a new successful login history entry
    /// </summary>
    public static UserLoginHistory CreateSuccessful(
        Guid userId,
        User user,
        string email,
        string phoneNumber,
        string ipAddress,
        string userAgent,
        string deviceInfo,
        string location,
        LoginMethod loginMethod,
        string createdBy)
    {
        return new UserLoginHistory
        {
            UserId = userId,
            User = user,
            Email = email,
            PhoneNumber = phoneNumber ?? string.Empty,
            IsSuccessful = true,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            DeviceInfo = deviceInfo,
            Location = location,
            LoginMethod = loginMethod,
            CreatedBy = createdBy
        };
    }

    /// <summary>
    /// Creates a new failed login history entry
    /// </summary>
    public static UserLoginHistory CreateFailed(
        Guid? userId,
        User user,
        string email,
        string phoneNumber,
        string failureReason,
        string ipAddress,
        string userAgent,
        string deviceInfo,
        string location,
        LoginMethod loginMethod,
        string createdBy)
    {
        return new UserLoginHistory
        {
            UserId = userId,
            User = user,
            Email = email,
            PhoneNumber = phoneNumber ?? string.Empty,
            IsSuccessful = false,
            FailureReason = failureReason,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            DeviceInfo = deviceInfo,
            Location = location,
            LoginMethod = loginMethod,
            CreatedBy = createdBy
        };
    }
}
