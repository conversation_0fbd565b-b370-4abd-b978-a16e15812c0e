using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Roles.Queries
{
    /// <summary>
    /// Query to get the roles for a user
    /// </summary>
    public class GetUserRolesQuery : IRequest<List<string>>
    {
        /// <summary>
        /// The user ID
        /// </summary>
        public Guid UserId { get; set; }
    }

    /// <summary>
    /// Handler for <see cref="GetUserRolesQuery"/>
    /// </summary>
    public class GetUserRolesQueryHandler : IRequestHandler<GetUserRolesQuery, List<string>>
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<GetUserRolesQueryHandler> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetUserRolesQueryHandler"/> class
        /// </summary>
        /// <param name="userRepository">The user repository</param>
        /// <param name="logger">The logger</param>
        public GetUserRolesQueryHandler(
            IUserRepository userRepository,
            ILogger<GetUserRolesQueryHandler> logger)
        {
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        /// <param name="request">The request</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>The user's roles</returns>
        public async Task<List<string>> Handle(GetUserRolesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting roles for user {UserId}", request.UserId);

                var user = await _userRepository.GetByIdAsync(request.UserId);

                if (user == null)
                {
                    _logger.LogWarning("User {UserId} not found", request.UserId);
                    return new List<string>();
                }

                var roles = await _userRepository.GetUserRolesAsync(request.UserId);

                if (roles == null || !roles.Any())
                {
                    _logger.LogWarning("No roles found for user {UserId}", request.UserId);
                    return new List<string>();
                }

                _logger.LogInformation("Successfully retrieved {Count} roles for user {UserId}", roles.Count, request.UserId);

                return roles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for user {UserId}", request.UserId);
                throw;
            }
        }
    }
}
