using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace SubscriptionService.Infrastructure
{
    public class CheckConnectionString
    {
        public static void Main(string[] args)
        {
            // Build configuration from appsettings.json in the API project
            var configurationBuilder = new ConfigurationBuilder()
                .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../SubscriptionService.API"))
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Development.json", optional: true);

            var configuration = configurationBuilder.Build();
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            Console.WriteLine($"Connection string: {connectionString}");
        }
    }
}
