using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace IdentityService.Infrastructure.Services;

/// <summary>
/// Service for managing user sessions
/// </summary>
public class SessionManagementService : ISessionManagementService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<SessionManagementService> _logger;
    private readonly SessionManagementOptions _options;

    public SessionManagementService(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ILogger<SessionManagementService> logger,
        IOptions<SessionManagementOptions> options)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _logger = logger;
        _options = options.Value;
    }

    /// <inheritdoc />
    public async Task<UserSession> CreateSessionAsync(
        Guid userId,
        string token,
        string refreshToken,
        DateTime expiresAt,
        string ipAddress,
        string userAgent,
        string deviceInfo)
    {
        var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
        if (user == null)
        {
            throw new ArgumentException($"User with ID {userId} not found");
        }

        // Check if we need to enforce the maximum number of concurrent sessions
        if (_options.MaxConcurrentSessions > 0)
        {
            var activeSessionCount = await _unitOfWork.UserSessionRepository.GetActiveSessionCountForUserAsync(userId);
            if (activeSessionCount >= _options.MaxConcurrentSessions)
            {
                // If we're preventing concurrent logins, terminate all existing sessions
                if (_options.PreventConcurrentLogin)
                {
                    await EndAllSessionsForUserAsync(userId);
                }
                else
                {
                    // Otherwise, terminate the oldest sessions to make room for the new one
                    var activeSessions = await _unitOfWork.UserSessionRepository.GetActiveSessionsForUserAsync(userId);
                    var sessionsToTerminate = activeSessions
                        .OrderBy(s => s.LastActiveAt)
                        .Take(activeSessionCount - _options.MaxConcurrentSessions + 1);

                    foreach (var oldSession in sessionsToTerminate)
                    {
                        oldSession.Terminate("Exceeded maximum concurrent sessions", "System");
                        await _unitOfWork.UserSessionRepository.UpdateAsync(oldSession);
                    }
                }
            }
        }

        // Create the new session
        var createdBy = _currentUserService.UserId.ToString() ?? "System";
        var session = UserSession.Create(
            userId,
            user,
            token,
            refreshToken,
            expiresAt,
            ipAddress,
            userAgent,
            deviceInfo,
            createdBy);

        await _unitOfWork.UserSessionRepository.AddAsync(session);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Created new session {SessionId} for user {UserId}", session.Id, userId);
        return session;
    }

    /// <inheritdoc />
    public async Task<bool> ValidateSessionAsync(string token)
    {
        var session = await _unitOfWork.UserSessionRepository.GetByTokenAsync(token);
        if (session == null)
        {
            _logger.LogWarning("Session with token {Token} not found", token);
            return false;
        }

        if (!session.IsActive())
        {
            _logger.LogWarning("Session {SessionId} is not active", session.Id);
            return false;
        }

        if (session.IsIdle(_options.IdleTimeoutMinutes))
        {
            _logger.LogWarning("Session {SessionId} is idle", session.Id);
            await ExpireSessionAsync(session);
            return false;
        }

        // Update the last active time
        session.UpdateLastActive();
        await _unitOfWork.UserSessionRepository.UpdateAsync(session);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    /// <inheritdoc />
    public async Task<UserSession> GetSessionByTokenAsync(string token)
    {
        return await _unitOfWork.UserSessionRepository.GetByTokenAsync(token);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<UserSession>> GetActiveSessionsForUserAsync(Guid userId)
    {
        return await _unitOfWork.UserSessionRepository.GetActiveSessionsForUserAsync(userId);
    }

    /// <inheritdoc />
    public async Task UpdateSessionActivityAsync(string token)
    {
        var session = await _unitOfWork.UserSessionRepository.GetByTokenAsync(token);
        if (session == null)
        {
            _logger.LogWarning("Session with token {Token} not found", token);
            return;
        }

        session.UpdateLastActive();
        await _unitOfWork.UserSessionRepository.UpdateAsync(session);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogDebug("Updated last active time for session {SessionId}", session.Id);
    }

    /// <inheritdoc />
    public async Task EndSessionAsync(string token)
    {
        var session = await _unitOfWork.UserSessionRepository.GetByTokenAsync(token);
        if (session == null)
        {
            _logger.LogWarning("Session with token {Token} not found", token);
            return;
        }

        var updatedBy = _currentUserService.UserId.ToString() ?? "System";
        session.End("User logout", updatedBy);
        await _unitOfWork.UserSessionRepository.UpdateAsync(session);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Ended session {SessionId} for user {UserId}", session.Id, session.UserId);
    }

    /// <inheritdoc />
    public async Task EndAllSessionsForUserAsync(Guid userId, string? currentToken = null)
    {
        var activeSessions = await _unitOfWork.UserSessionRepository.GetActiveSessionsForUserAsync(userId);
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";

        foreach (var session in activeSessions)
        {
            // Skip the current session if a token is provided
            if (!string.IsNullOrEmpty(currentToken) && session.Token == currentToken)
            {
                continue;
            }

            session.End("User logged out all sessions", updatedBy);
            await _unitOfWork.UserSessionRepository.UpdateAsync(session);
        }

        await _unitOfWork.SaveChangesAsync();
        _logger.LogInformation("Ended all sessions for user {UserId}", userId);
    }

    /// <inheritdoc />
    public async Task TerminateSessionAsync(string token, string reason)
    {
        var session = await _unitOfWork.UserSessionRepository.GetByTokenAsync(token);
        if (session == null)
        {
            _logger.LogWarning("Session with token {Token} not found", token);
            return;
        }

        var updatedBy = _currentUserService.UserId.ToString() ?? "System";
        session.Terminate(reason, updatedBy);
        await _unitOfWork.UserSessionRepository.UpdateAsync(session);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Terminated session {SessionId} for user {UserId}: {Reason}",
            session.Id, session.UserId, reason);
    }

    /// <inheritdoc />
    public async Task<bool> IsSessionIdleAsync(string token)
    {
        var session = await _unitOfWork.UserSessionRepository.GetByTokenAsync(token);
        if (session == null)
        {
            _logger.LogWarning("Session with token {Token} not found", token);
            return true; // Consider non-existent sessions as idle
        }

        return session.IsIdle(_options.IdleTimeoutMinutes);
    }

    /// <inheritdoc />
    public async Task ExpireIdleSessionsAsync()
    {
        var idleSessions = await _unitOfWork.UserSessionRepository.GetIdleSessionsAsync(_options.IdleTimeoutMinutes);
        var expiredSessions = await _unitOfWork.UserSessionRepository.GetExpiredSessionsAsync();

        // Combine idle and expired sessions
        var sessionsToExpire = idleSessions.Concat(expiredSessions).Distinct();

        foreach (var session in sessionsToExpire)
        {
            await ExpireSessionAsync(session);
        }

        await _unitOfWork.SaveChangesAsync();
        _logger.LogInformation("Expired {Count} idle or expired sessions", sessionsToExpire.Count());
    }

    /// <inheritdoc />
    public async Task<IEnumerable<UserSession>> GetSessionHistoryForUserAsync(
        Guid userId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 10)
    {
        return await _unitOfWork.UserSessionRepository.GetSessionsForUserAsync(
            userId, fromDate, toDate, null, pageNumber, pageSize);
    }

    private async Task ExpireSessionAsync(UserSession session)
    {
        session.Expire("System");
        await _unitOfWork.UserSessionRepository.UpdateAsync(session);
        _logger.LogInformation("Expired session {SessionId} for user {UserId}", session.Id, session.UserId);
    }
}
