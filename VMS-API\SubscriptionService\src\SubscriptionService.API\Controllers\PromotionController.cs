using Microsoft.AspNetCore.Mvc;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PromotionController : ControllerBase
    {
        private readonly IPromotionService _promotionService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PromotionController> _logger;

        public PromotionController(
            IPromotionService promotionService,
            IUnitOfWork unitOfWork,
            ILogger<PromotionController> logger)
        {
            _promotionService = promotionService;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<PromotionCode>>> GetActivePromotions()
        {
            try
            {
                var promotions = await _promotionService.GetActivePromotionsAsync();
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active promotions");
                return StatusCode(500, "An error occurred while retrieving promotions");
            }
        }

        [HttpGet("plan/{planId}")]
        public async Task<ActionResult<IEnumerable<PromotionCode>>> GetPromotionsForPlan(Guid planId)
        {
            try
            {
                var promotions = await _promotionService.GetPromotionsForPlanAsync(planId);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting promotions for plan {PlanId}", planId);
                return StatusCode(500, "An error occurred while retrieving promotions");
            }
        }

        [HttpGet("{code}")]
        public async Task<ActionResult<PromotionCode>> GetPromotionByCode(string code)
        {
            try
            {
                var promotion = await _unitOfWork.PromotionCodes.GetByCodeAsync(code);
                if (promotion == null)
                {
                    return NotFound($"Promotion code {code} not found");
                }
                return Ok(promotion);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting promotion code {Code}", code);
                return StatusCode(500, "An error occurred while retrieving the promotion code");
            }
        }

        [HttpPost]
        public async Task<ActionResult<PromotionCode>> CreatePromotionCode([FromBody] CreatePromotionRequest request)
        {
            try
            {
                var promotion = await _promotionService.CreatePromotionCodeAsync(
                    request.Code,
                    request.Description,
                    request.DiscountType,
                    request.DiscountValue,
                    request.StartDate,
                    request.EndDate,
                    request.MaxUsageCount,
                    request.SubscriptionPlanId,
                    request.MinimumBillingCycles);

                return CreatedAtAction(nameof(GetPromotionByCode), new { code = promotion.Code }, promotion);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating promotion code {Code}", request.Code);
                return StatusCode(500, "An error occurred while creating the promotion code");
            }
        }

        [HttpPost("{code}/validate")]
        public async Task<ActionResult<bool>> ValidatePromotionCode(string code, [FromBody] ValidatePromotionRequest request)
        {
            try
            {
                var isValid = await _promotionService.ValidatePromotionCodeAsync(code, request.SubscriptionPlanId);
                return Ok(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating promotion code {Code}", code);
                return StatusCode(500, "An error occurred while validating the promotion code");
            }
        }

        [HttpDelete("{code}")]
        public async Task<ActionResult> DeactivatePromotionCode(string code)
        {
            try
            {
                await _promotionService.DeactivatePromotionCodeAsync(code);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating promotion code {Code}", code);
                return StatusCode(500, "An error occurred while deactivating the promotion code");
            }
        }
    }

    public class CreatePromotionRequest
    {
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DiscountType DiscountType { get; set; }
        public decimal DiscountValue { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? MaxUsageCount { get; set; }
        public Guid? SubscriptionPlanId { get; set; }
        public int? MinimumBillingCycles { get; set; }
    }

    public class ValidatePromotionRequest
    {
        public Guid? SubscriptionPlanId { get; set; }
    }
}
