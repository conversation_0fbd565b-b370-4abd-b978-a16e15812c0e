using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SubscriptionService.API.Models
{
    public class CustomSubscriptionRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one plan must be selected")]
        public List<PlanSelection> SelectedPlans { get; set; } = new List<PlanSelection>();
        
        [Required]
        public Guid VendorId { get; set; }
        
        public string? PromotionCode { get; set; }
        
        [Required]
        public DateTime StartDate { get; set; } = DateTime.UtcNow;
        
        [Required]
        public int DurationInMonths { get; set; } = 12; // Default to 1 year
        
        [Range(0, 30, ErrorMessage = "Grace period days must be between 0 and 30")]
        public int GracePeriodDays { get; set; } = 7; // Default to 7 days
    }

    public class PlanSelection
    {
        [Required]
        public Guid PlanId { get; set; }
        
        [Required]
        [Range(1, 100, ErrorMessage = "Branch count must be between 1 and 100")]
        public int BranchCount { get; set; }
        
        [Required]
        [Range(1, 100, ErrorMessage = "Users per branch must be between 1 and 100")]
        public int UsersPerBranch { get; set; }
    }
}
