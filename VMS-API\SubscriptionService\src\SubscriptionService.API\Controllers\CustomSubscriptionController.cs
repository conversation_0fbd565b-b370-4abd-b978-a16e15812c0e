using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SubscriptionService.API.Models;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SubscriptionService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CustomSubscriptionController : ControllerBase
    {
        private readonly SubscriptionDbContext _context;

        public CustomSubscriptionController(SubscriptionDbContext context)
        {
            _context = context;
        }

        [HttpPost("calculate")]
        public async Task<ActionResult<SubscriptionSummary>> CalculateSubscription([FromBody] CustomSubscriptionRequest request)
        {
            try
            {
                // Validate request
                if (request.SelectedPlans == null || !request.SelectedPlans.Any())
                {
                    return BadRequest("At least one plan must be selected");
                }

                // Get all selected plan IDs
                var planIds = request.SelectedPlans.Select(p => p.PlanId).ToList();

                // Fetch all selected plans with their features
                var plans = await _context.SubscriptionPlans
                    .Where(p => planIds.Contains(p.Id) && p.IsActive)
                    .Include(p => p.FeatureAccesses)
                    .Include(p => p.PricingModels)
                    .ToListAsync();

                // Validate that all requested plans exist and are active
                if (plans.Count != planIds.Count)
                {
                    return BadRequest("One or more selected plans are invalid or inactive");
                }

                // Calculate end date based on duration
                var endDate = request.StartDate.AddMonths(request.DurationInMonths);

                // Create the summary
                var summary = new SubscriptionSummary
                {
                    VendorId = request.VendorId,
                    StartDate = request.StartDate,
                    EndDate = endDate,
                    DurationInMonths = request.DurationInMonths,
                    GracePeriodDays = request.GracePeriodDays,
                    BillingCycle = BillingCycle.Monthly, // Default to monthly
                    AppliedPromotionCode = request.PromotionCode,
                    DiscountAmount = 0, // Will be calculated if promotion code is valid
                    GrandTotal = 0 // Will be calculated below
                };

                // Process each selected plan
                foreach (var selection in request.SelectedPlans)
                {
                    var plan = plans.FirstOrDefault(p => p.Id == selection.PlanId);
                    if (plan == null) continue; // Should not happen due to earlier validation

                    // Calculate base per-branch price based on plan tier
                    decimal basePerBranchPrice = plan.Tier switch
                    {
                        SubscriptionTier.Basic => 1000, // ₹1,000 per branch for Basic
                        SubscriptionTier.Silver => 1500, // ₹1,500 per branch for Silver
                        SubscriptionTier.Gold => 2000, // ₹2,000 per branch for Gold
                        _ => 1000 // Default to ₹1,000 for other tiers
                    };

                    // Calculate price per user
                    decimal pricePerUser = plan.Tier switch
                    {
                        SubscriptionTier.Basic => 100, // ₹100 per user for Basic
                        SubscriptionTier.Silver => 150, // ₹150 per user for Silver
                        SubscriptionTier.Gold => 200, // ₹200 per user for Gold
                        _ => 100 // Default to ₹100 for other tiers
                    };

                    // Calculate adjusted per-branch price based on users per branch
                    // Base price + additional cost for users beyond the standard allocation
                    int standardUsersPerBranch = plan.Tier switch
                    {
                        SubscriptionTier.Basic => 5,  // Standard is 5 users per branch for Basic
                        SubscriptionTier.Silver => 10, // Standard is 10 users per branch for Silver
                        SubscriptionTier.Gold => 15,  // Standard is 15 users per branch for Gold
                        _ => 5
                    };

                    // Calculate extra users beyond standard allocation
                    int extraUsersPerBranch = Math.Max(0, selection.UsersPerBranch - standardUsersPerBranch);

                    // Calculate final per-branch price including extra users
                    decimal pricePerBranch = basePerBranchPrice + (extraUsersPerBranch * pricePerUser);

                    // Calculate total price for this plan
                    decimal totalPlanPrice = pricePerBranch * selection.BranchCount;

                    // Calculate total users
                    int totalUsers = selection.BranchCount * selection.UsersPerBranch;

                    // Create plan summary
                    var planSummary = new PlanSummary
                    {
                        PlanId = plan.Id,
                        PlanName = plan.Name,
                        PlanDescription = plan.Description,
                        Tier = plan.Tier,
                        BranchCount = selection.BranchCount,
                        UsersPerBranch = selection.UsersPerBranch,
                        TotalUsers = totalUsers,
                        StandardUsersPerBranch = standardUsersPerBranch,
                        ExtraUsersPerBranch = extraUsersPerBranch,
                        BasePerBranchPrice = basePerBranchPrice,
                        PricePerExtraUser = pricePerUser,
                        PricePerBranch = pricePerBranch,
                        TotalPrice = totalPlanPrice,
                        FormattedBasePrice = $"₹{basePerBranchPrice:N0}",
                        FormattedPricePerBranch = $"₹{pricePerBranch:N0}",
                        FormattedTotalPrice = $"₹{totalPlanPrice:N0}"
                    };

                    // Add features to the plan summary
                    foreach (var feature in plan.FeatureAccesses.Where(f => f.IsEnabled))
                    {
                        planSummary.Features.Add(new FeatureSummary
                        {
                            FeatureName = feature.FeatureName,
                            IsEnabled = feature.IsEnabled,
                            UsageLimit = feature.UsageLimit
                        });
                    }

                    // Add plan summary to the overall summary
                    summary.SelectedPlans.Add(planSummary);

                    // Add to grand total
                    summary.GrandTotal += totalPlanPrice;
                }

                // Format grand total
                summary.FormattedGrandTotal = $"₹{summary.GrandTotal:N0}";

                return Ok(summary);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("create")]
        public async Task<ActionResult<Guid>> CreateSubscription([FromBody] CustomSubscriptionRequest request)
        {
            try
            {
                // First calculate the subscription summary
                var calculationResult = await CalculateSubscription(request);

                if (calculationResult.Result is ObjectResult objectResult &&
                    objectResult.StatusCode != 200)
                {
                    return objectResult; // Return any error from calculation
                }

                var summary = (calculationResult.Result as ObjectResult)?.Value as SubscriptionSummary;
                if (summary == null)
                {
                    return StatusCode(500, "Failed to calculate subscription summary");
                }

                // Create a subscription for each selected plan
                var subscriptionIds = new List<Guid>();

                foreach (var planSummary in summary.SelectedPlans)
                {
                    // Fetch the subscription plan from the database
                    var plan = await _context.SubscriptionPlans.FindAsync(planSummary.PlanId);
                    if (plan == null)
                    {
                        return NotFound($"Subscription plan with ID {planSummary.PlanId} not found");
                    }

                    var subscription = new Subscription(
                        request.VendorId,
                        request.VendorId, // Using VendorId as UserId for simplicity
                        planSummary.PlanId,
                        summary.StartDate,
                        summary.EndDate,
                        summary.BillingCycle,
                        planSummary.TotalPrice,
                        summary.GracePeriodDays
                    )
                    {
                        SubscriptionPlan = plan // Set the required SubscriptionPlan property
                    };

                    _context.Subscriptions.Add(subscription);
                    subscriptionIds.Add(subscription.Id);
                }

                await _context.SaveChangesAsync();

                // Return the IDs of the created subscriptions
                return Ok(new { SubscriptionIds = subscriptionIds });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
