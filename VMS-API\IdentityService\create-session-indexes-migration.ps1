# PowerShell script to create migration for session performance indexes
# Run this from the IdentityService directory

Write-Host "Creating migration for session performance indexes..." -ForegroundColor Green

# Navigate to the correct directory
Set-Location "src\IdentityService.Infrastructure"

# Create the migration
dotnet ef migrations add OptimizeSessionIndexes --startup-project ..\IdentityService.API

Write-Host "Migration created successfully!" -ForegroundColor Green
Write-Host "To apply the migration, run:" -ForegroundColor Yellow
Write-Host "dotnet ef database update --startup-project ..\IdentityService.API" -ForegroundColor Yellow
