# Set the API project directory
$apiDir = Join-Path $PSScriptRoot "src\BranchManagementService.API"
Write-Host "API directory: $apiDir"

# Build the project in Debug configuration
Write-Host "Building the API in Debug configuration..."
Set-Location $apiDir
dotnet build -c Debug

# Run the API with debugging enabled
Write-Host "Starting the API with debugging enabled..."
dotnet run --no-build --urls="http://localhost:5004" --launch-profile Development
