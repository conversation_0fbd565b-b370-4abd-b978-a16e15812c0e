using Microsoft.EntityFrameworkCore;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.Infrastructure.Persistence.Repositories
{
    public class SubscriptionChangeRepository : DomainBaseRepository<SubscriptionChange>, ISubscriptionChangeRepository
    {
        public SubscriptionChangeRepository(SubscriptionDbContext context) : base(context)
        {
        }

        public async Task<IReadOnlyList<SubscriptionChange>> GetChangesBySubscriptionIdAsync(Guid subscriptionId)
        {
            return await _dbSet
                .Where(c => c.SubscriptionId == subscriptionId)
                .OrderByDescending(c => c.EffectiveDate)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<SubscriptionChange>> GetChangesByTypeAsync(ChangeType changeType)
        {
            return await _dbSet
                .Where(c => c.ChangeType == changeType)
                .OrderByDescending(c => c.EffectiveDate)
                .ToListAsync();
        }

        public async Task<SubscriptionChange?> GetLatestChangeForSubscriptionAsync(Guid subscriptionId)
        {
            return await _dbSet
                .Where(c => c.SubscriptionId == subscriptionId)
                .OrderByDescending(c => c.EffectiveDate)
                .FirstOrDefaultAsync();
        }
    }
}
