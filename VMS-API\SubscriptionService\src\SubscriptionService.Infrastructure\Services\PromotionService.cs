using Microsoft.Extensions.Logging;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.Infrastructure.Services
{
    public class PromotionService : IPromotionService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PromotionService> _logger;

        public PromotionService(
            IUnitOfWork unitOfWork,
            ILogger<PromotionService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<PromotionCode> CreatePromotionCodeAsync(
            string code,
            string description,
            DiscountType discountType,
            decimal discountValue,
            DateTime startDate,
            DateTime? endDate = null,
            int? maxUsageCount = null,
            Guid? subscriptionPlanId = null,
            int? minimumBillingCycles = null)
        {
            _logger.LogInformation("Creating promotion code {Code}", code);

            // Check if the code already exists
            var existingCode = await _unitOfWork.PromotionCodes.GetByCodeAsync(code);
            if (existingCode != null)
            {
                throw new ArgumentException($"Promotion code {code} already exists");
            }

            // Validate the subscription plan if provided
            if (subscriptionPlanId.HasValue)
            {
                var plan = await _unitOfWork.SubscriptionPlans.GetByIdAsync(subscriptionPlanId.Value);
                if (plan == null)
                {
                    throw new ArgumentException($"Subscription plan with ID {subscriptionPlanId} not found");
                }
            }

            // Create the promotion code
            var promotionCode = new PromotionCode(
                code,
                description,
                discountType,
                discountValue,
                startDate,
                endDate,
                maxUsageCount,
                subscriptionPlanId,
                minimumBillingCycles);

            // Save the promotion code
            await _unitOfWork.PromotionCodes.AddAsync(promotionCode);
            await _unitOfWork.SaveChangesAsync();

            return promotionCode;
        }

        public async Task<bool> ValidatePromotionCodeAsync(string code, Guid? subscriptionPlanId = null)
        {
            return await _unitOfWork.PromotionCodes.IsCodeValidAsync(code, subscriptionPlanId);
        }

        public async Task<decimal> CalculateDiscountAsync(string code, decimal originalAmount)
        {
            var promotionCode = await _unitOfWork.PromotionCodes.GetByCodeAsync(code);
            if (promotionCode == null || !promotionCode.IsValid())
            {
                return 0;
            }

            return promotionCode.CalculateDiscount(originalAmount);
        }

        public async Task<PromotionCode> UsePromotionCodeAsync(string code)
        {
            var promotionCode = await _unitOfWork.PromotionCodes.GetByCodeAsync(code);
            if (promotionCode == null)
            {
                throw new ArgumentException($"Promotion code {code} not found");
            }

            if (!promotionCode.IsValid())
            {
                throw new ArgumentException($"Promotion code {code} is not valid");
            }

            promotionCode.IncrementUsageCount();
            await _unitOfWork.SaveChangesAsync();

            return promotionCode;
        }

        public async Task<IReadOnlyList<PromotionCode>> GetActivePromotionsAsync()
        {
            return await _unitOfWork.PromotionCodes.GetActivePromotionsAsync();
        }

        public async Task<IReadOnlyList<PromotionCode>> GetPromotionsForPlanAsync(Guid planId)
        {
            return await _unitOfWork.PromotionCodes.GetPromotionsByPlanIdAsync(planId);
        }

        public async Task DeactivatePromotionCodeAsync(string code)
        {
            var promotionCode = await _unitOfWork.PromotionCodes.GetByCodeAsync(code);
            if (promotionCode == null)
            {
                throw new ArgumentException($"Promotion code {code} not found");
            }

            promotionCode.Deactivate();
            await _unitOfWork.SaveChangesAsync();
        }
    }
}
