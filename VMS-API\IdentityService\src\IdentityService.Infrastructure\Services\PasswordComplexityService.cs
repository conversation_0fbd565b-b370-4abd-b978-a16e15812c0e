using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Services;
using Microsoft.Extensions.Options;

namespace IdentityService.Infrastructure.Services;

/// <summary>
/// Implementation of the password complexity service
/// </summary>
public class PasswordComplexityService : IPasswordComplexityService
{
    private readonly PasswordComplexityOptions _options;
    private static readonly HashSet<string> CommonPasswords = new(StringComparer.OrdinalIgnoreCase)
    {
        "password", "123456", "12345678", "qwerty", "abc123", "monkey", "1234567", "letmein",
        "trustno1", "dragon", "baseball", "111111", "iloveyou", "master", "sunshine", "ashley",
        "bailey", "passw0rd", "shadow", "123123", "654321", "superman", "qazwsx", "michael",
        "football", "welcome", "jesus", "ninja", "mustang", "password1", "123456789", "adobe123"
    };

    public PasswordComplexityService(IOptions<PasswordComplexityOptions> options)
    {
        _options = options.Value;
    }

    /// <inheritdoc />
    public (bool IsValid, IEnumerable<string> Errors) ValidatePassword(string password, string? username = null)
    {
        var errors = new List<string>();

        // Check for null or empty password
        if (string.IsNullOrEmpty(password))
        {
            errors.Add("Password cannot be empty.");
            return (false, errors);
        }

        // Check minimum length
        if (password.Length < _options.MinimumLength)
        {
            errors.Add($"Password must be at least {_options.MinimumLength} characters long.");
        }

        // Check for uppercase letters
        if (_options.RequireUppercase && !password.Any(char.IsUpper))
        {
            errors.Add("Password must contain at least one uppercase letter.");
        }

        // Check for lowercase letters
        if (_options.RequireLowercase && !password.Any(char.IsLower))
        {
            errors.Add("Password must contain at least one lowercase letter.");
        }

        // Check for digits
        if (_options.RequireDigit && !password.Any(char.IsDigit))
        {
            errors.Add("Password must contain at least one digit.");
        }

        // Check for special characters
        if (_options.RequireSpecialCharacter && !password.Any(c => !char.IsLetterOrDigit(c)))
        {
            errors.Add("Password must contain at least one special character.");
        }

        // Check for repeated characters
        if (_options.MaximumRepeatedCharacters > 0)
        {
            var repeatedCharPattern = $"(.)\\1{{{_options.MaximumRepeatedCharacters},}}";
            if (Regex.IsMatch(password, repeatedCharPattern))
            {
                errors.Add($"Password cannot contain more than {_options.MaximumRepeatedCharacters} repeated characters in a row.");
            }
        }

        // Check for common passwords
        if (_options.PreventCommonPasswords && CommonPasswords.Contains(password))
        {
            errors.Add("Password is too common and easily guessable.");
        }

        // Check if password contains username
        if (_options.PreventUsernameInPassword && !string.IsNullOrEmpty(username) &&
            (password.Contains(username, StringComparison.OrdinalIgnoreCase) ||
             username.Contains(password, StringComparison.OrdinalIgnoreCase)))
        {
            errors.Add("Password cannot contain your username.");
        }

        return (errors.Count == 0, errors);
    }

    /// <inheritdoc />
    public PasswordComplexityOptions GetPasswordComplexityOptions()
    {
        return _options;
    }
}
