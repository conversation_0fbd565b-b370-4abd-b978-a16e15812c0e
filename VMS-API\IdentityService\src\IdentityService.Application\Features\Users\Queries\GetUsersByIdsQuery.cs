using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Users;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Queries
{
    /// <summary>
    /// Query to get users by IDs
    /// </summary>
    public class GetUsersByIdsQuery : IRequest<List<UserResponse>>
    {
        /// <summary>
        /// The user IDs
        /// </summary>
        public required List<Guid> UserIds { get; set; }
    }

    /// <summary>
    /// Handler for <see cref="GetUsersByIdsQuery"/>
    /// </summary>
    public class GetUsersByIdsQueryHandler : IRequestHandler<GetUsersByIdsQuery, List<UserResponse>>
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<GetUsersByIdsQueryHandler> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetUsersByIdsQueryHandler"/> class
        /// </summary>
        /// <param name="userRepository">The user repository</param>
        /// <param name="logger">The logger</param>
        public GetUsersByIdsQueryHandler(
            IUserRepository userRepository,
            ILogger<GetUsersByIdsQueryHandler> logger)
        {
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        /// <param name="request">The request</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>The user responses</returns>
        public async Task<List<UserResponse>> Handle(GetUsersByIdsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting users by IDs, count: {Count}", request.UserIds.Count);

                var users = await _userRepository.GetByIdsAsync(request.UserIds);

                if (users == null || !users.Any())
                {
                    _logger.LogWarning("No users found for the provided IDs");
                    return new List<UserResponse>();
                }

                var responses = users.Select(user => new UserResponse
                {
                    Id = user.Id,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    IsActive = !user.IsDeleted,
                    EmailVerified = user.EmailVerified,
                    PhoneNumberVerified = user.PhoneNumberVerified,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    CreatedBy = user.CreatedBy,
                    UpdatedBy = user.UpdatedBy
                }).ToList();

                _logger.LogInformation("Successfully retrieved {0} users", responses.Count);

                return responses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users by IDs");
                throw;
            }
        }
    }
}
