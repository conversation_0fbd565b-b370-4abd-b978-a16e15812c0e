using Microsoft.Extensions.Logging;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Interfaces;
using SubscriptionService.Infrastructure.Messaging.Events;

namespace SubscriptionService.Infrastructure.Services
{
    public class NotificationService : INotificationService
    {
        private readonly ILogger<NotificationService> _logger;
        private readonly IEventPublisher _eventPublisher;

        public NotificationService(
            ILogger<NotificationService> logger,
            IEventPublisher eventPublisher)
        {
            _logger = logger;
            _eventPublisher = eventPublisher;
        }

        public async Task SendSubscriptionRenewalReminderAsync(Subscription subscription, int daysLeft)
        {
            _logger.LogInformation("Sending subscription renewal reminder for subscription {SubscriptionId}, {DaysLeft} days left",
                subscription.Id, daysLeft);

            // In a real implementation, you would send an email here
            // For now, we'll just publish an event that can be consumed by an email service

            var @event = new SubscriptionRenewalReminderEvent(
                subscription.Id,
                subscription.UserId,
                subscription.EndDate,
                daysLeft);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendSubscriptionExpiredAsync(Subscription subscription)
        {
            _logger.LogInformation("Sending subscription expired notification for subscription {SubscriptionId}",
                subscription.Id);

            var @event = new Messaging.Events.SubscriptionExpiredEvent(
                subscription.Id,
                subscription.UserId);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendSubscriptionActivatedAsync(Subscription subscription)
        {
            _logger.LogInformation("Sending subscription activated notification for subscription {SubscriptionId}",
                subscription.Id);

            var @event = new SubscriptionActivatedEvent(
                subscription.Id,
                subscription.UserId,
                subscription.StartDate,
                subscription.EndDate);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendSubscriptionCancelledAsync(Subscription subscription)
        {
            _logger.LogInformation("Sending subscription cancelled notification for subscription {SubscriptionId}",
                subscription.Id);

            var @event = new SubscriptionCancelledEvent(
                subscription.Id,
                subscription.UserId);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendTrialEndingReminderAsync(Subscription subscription, int daysLeft)
        {
            _logger.LogInformation("Sending trial ending reminder for subscription {SubscriptionId}, {DaysLeft} days left",
                subscription.Id, daysLeft);

            var @event = new TrialEndingReminderEvent(
                subscription.Id,
                subscription.UserId,
                subscription.TrialEndDate.Value,
                daysLeft);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendInvoiceCreatedAsync(Invoice invoice, Subscription subscription)
        {
            _logger.LogInformation("Sending invoice created notification for invoice {InvoiceNumber}",
                invoice.InvoiceNumber);

            var @event = new InvoiceCreatedEvent(
                invoice.Id,
                invoice.InvoiceNumber,
                subscription.UserId,
                invoice.TotalAmount,
                invoice.DueDate);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendPaymentReceivedAsync(Invoice invoice, Subscription subscription)
        {
            _logger.LogInformation("Sending payment received notification for invoice {InvoiceNumber}",
                invoice.InvoiceNumber);

            var @event = new PaymentReceivedEvent(
                invoice.Id,
                invoice.InvoiceNumber,
                subscription.UserId,
                invoice.TotalAmount,
                invoice.PaymentDate.Value);

            await _eventPublisher.PublishAsync(@event);
        }

        public async Task SendPaymentOverdueAsync(Invoice invoice, Subscription subscription)
        {
            _logger.LogInformation("Sending payment overdue notification for invoice {InvoiceNumber}",
                invoice.InvoiceNumber);

            var @event = new PaymentOverdueEvent(
                invoice.Id,
                invoice.InvoiceNumber,
                subscription.UserId,
                invoice.TotalAmount,
                invoice.DueDate);

            await _eventPublisher.PublishAsync(@event);
        }
    }
}
