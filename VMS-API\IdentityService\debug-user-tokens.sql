-- Debug script to check password reset tokens for specific user
-- User ID: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
-- Email: <EMAIL>

-- 1. Check existing password reset tokens for this user
SELECT 
    prt."Id",
    prt."Token",
    prt."CreatedAt",
    prt."ExpiryTime",
    prt."IsUsed",
    prt."IsDeleted",
    prt."UpdatedAt",
    CASE 
        WHEN prt."IsUsed" = true THEN 'USED'
        WHEN prt."ExpiryTime" <= NOW() THEN 'EXPIRED'
        WHEN prt."IsDeleted" = true THEN 'DELETED'
        ELSE 'ACTIVE'
    END as "Status"
FROM "PasswordResetTokens" prt
WHERE prt."UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e'
ORDER BY prt."CreatedAt" DESC;

-- 2. Check user details
SELECT 
    u."Id",
    u."Email",
    u."CreatedAt",
    u."UpdatedAt",
    u."LastLoginAt",
    u."IsDeleted"
FROM "Users" u
WHERE u."Id" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';

-- 3. Clean up expired/used tokens for this user (RECOMMENDED - run this)
DELETE FROM "PasswordResetTokens"
WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e'
AND ("IsUsed" = true OR "ExpiryTime" <= NOW() OR "IsDeleted" = true);

-- 3b. If still having issues, clean up ALL tokens for this user (NUCLEAR OPTION)
-- DELETE FROM "PasswordResetTokens"
-- WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';

-- 4. Count total tokens for this user
SELECT 
    COUNT(*) as "TotalTokens",
    COUNT(CASE WHEN "IsUsed" = false AND "ExpiryTime" > NOW() AND "IsDeleted" = false THEN 1 END) as "ActiveTokens",
    COUNT(CASE WHEN "IsUsed" = true THEN 1 END) as "UsedTokens",
    COUNT(CASE WHEN "ExpiryTime" <= NOW() THEN 1 END) as "ExpiredTokens"
FROM "PasswordResetTokens"
WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';
