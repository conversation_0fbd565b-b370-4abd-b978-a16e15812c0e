using BranchManagementService.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BranchManagementService.Domain.Interfaces
{
    public interface IBranchRepository : IRepository<Branch>
    {
        Task<IReadOnlyList<Branch>> GetByVendorIdAsync(Guid vendorId);
        Task<int> GetBranchCountAsync(Guid vendorId);
        Task<bool> IsBranchLimitReachedAsync(Guid vendorId, int limit);
    }
}
