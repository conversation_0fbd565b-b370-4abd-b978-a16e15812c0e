using Microsoft.EntityFrameworkCore;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.Infrastructure.Persistence.Repositories
{
    public class PromotionCodeRepository : DomainBaseRepository<PromotionCode>, IPromotionCodeRepository
    {
        public PromotionCodeRepository(SubscriptionDbContext context) : base(context)
        {
        }

        public async Task<PromotionCode?> GetByCodeAsync(string code)
        {
            return await _dbSet
                .FirstOrDefaultAsync(p => p.Code == code);
        }

        public async Task<IReadOnlyList<PromotionCode>> GetActivePromotionsAsync()
        {
            var now = DateTime.UtcNow;
            return await _dbSet
                .Where(p => p.IsActive &&
                           p.StartDate <= now &&
                           (!p.EndDate.HasValue || p.EndDate >= now) &&
                           (!p.MaxUsageCount.HasValue || p.CurrentUsageCount < p.MaxUsageCount.Value))
                .OrderBy(p => p.EndDate)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<PromotionCode>> GetPromotionsByPlanIdAsync(Guid planId)
        {
            var now = DateTime.UtcNow;
            return await _dbSet
                .Where(p => p.IsActive &&
                           p.StartDate <= now &&
                           (!p.EndDate.HasValue || p.EndDate >= now) &&
                           (!p.MaxUsageCount.HasValue || p.CurrentUsageCount < p.MaxUsageCount.Value) &&
                           (!p.SubscriptionPlanId.HasValue || p.SubscriptionPlanId == planId))
                .OrderBy(p => p.EndDate)
                .ToListAsync();
        }

        public async Task<bool> IsCodeValidAsync(string code, Guid? planId = null)
        {
            var promotion = await GetByCodeAsync(code);

            if (promotion == null)
                return false;

            if (!promotion.IsValid())
                return false;

            if (planId.HasValue && promotion.SubscriptionPlanId.HasValue &&
                promotion.SubscriptionPlanId.Value != planId.Value)
                return false;

            return true;
        }
    }
}
