using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.Infrastructure.Services
{
    public class SubscriptionRenewalService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SubscriptionRenewalService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromHours(24); // Run once a day

        public SubscriptionRenewalService(
            IServiceProvider serviceProvider,
            ILogger<SubscriptionRenewalService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Subscription Renewal Service is starting");

            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Checking for subscriptions that need attention");

                try
                {
                    await ProcessSubscriptionsAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while processing subscriptions");
                }

                _logger.LogInformation("Subscription check completed. Waiting for next check interval");
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task ProcessSubscriptionsAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
            var billingService = scope.ServiceProvider.GetRequiredService<IBillingService>();

            // Process subscriptions that need renewal
            await ProcessRenewalsAsync(unitOfWork, notificationService);

            // Process subscriptions that are expiring soon
            await ProcessExpiringSubscriptionsAsync(unitOfWork, notificationService);

            // Process trial subscriptions that are ending soon
            await ProcessTrialEndingSubscriptionsAsync(unitOfWork, notificationService);

            // Process overdue invoices
            await billingService.ProcessDueInvoicesAsync();
        }

        private async Task ProcessRenewalsAsync(IUnitOfWork unitOfWork, INotificationService notificationService)
        {
            var today = DateTime.UtcNow.Date;

            // Get subscriptions due for renewal (next billing date is today or earlier)
            var subscriptionsDueForRenewal = await unitOfWork.Subscriptions.GetSubscriptionsDueForBillingAsync();

            foreach (var subscription in subscriptionsDueForRenewal)
            {
                _logger.LogInformation("Processing renewal for subscription {SubscriptionId}", subscription.Id);

                try
                {
                    // Generate invoice for the renewal
                    var plan = await unitOfWork.SubscriptionPlans.GetByIdAsync(subscription.SubscriptionPlanId);
                    if (plan == null)
                    {
                        _logger.LogWarning("Plan not found for subscription {SubscriptionId}", subscription.Id);
                        continue;
                    }

                    var amount = plan.Price;
                    var taxAmount = amount * 0.18m; // Assuming 18% tax

                    await unitOfWork.Invoices.GenerateInvoiceNumberAsync();

                    // Update the subscription's next billing date
                    subscription.ProcessBilling();
                    await unitOfWork.SaveChangesAsync();

                    _logger.LogInformation("Renewal processed for subscription {SubscriptionId}", subscription.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing renewal for subscription {SubscriptionId}", subscription.Id);
                }
            }
        }

        private async Task ProcessExpiringSubscriptionsAsync(IUnitOfWork unitOfWork, INotificationService notificationService)
        {
            var today = DateTime.UtcNow.Date;

            // Get active subscriptions that expire in 30, 15, 7, 3, and 1 days
            var reminderDays = new[] { 30, 15, 7, 3, 1 };

            foreach (var daysLeft in reminderDays)
            {
                var expiryDate = today.AddDays(daysLeft);
                var expiringSubscriptions = await unitOfWork.Subscriptions.GetAsync(
                    s => s.Status == SubscriptionStatus.Active &&
                         s.EndDate.Date == expiryDate);

                foreach (var subscription in expiringSubscriptions)
                {
                    _logger.LogInformation("Sending expiration reminder for subscription {SubscriptionId}, {DaysLeft} days left",
                        subscription.Id, daysLeft);

                    try
                    {
                        await notificationService.SendSubscriptionRenewalReminderAsync(subscription, daysLeft);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending expiration reminder for subscription {SubscriptionId}",
                            subscription.Id);
                    }
                }
            }

            // Process expired subscriptions
            var expiredSubscriptions = await unitOfWork.Subscriptions.GetAsync(
                s => s.Status == SubscriptionStatus.Active &&
                     s.EndDate.Date < today);

            foreach (var subscription in expiredSubscriptions)
            {
                _logger.LogInformation("Processing expired subscription {SubscriptionId}", subscription.Id);

                try
                {
                    // Update subscription status
                    subscription.EnterGracePeriod();
                    await unitOfWork.SaveChangesAsync();

                    // Send notification
                    await notificationService.SendSubscriptionExpiredAsync(subscription);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing expired subscription {SubscriptionId}", subscription.Id);
                }
            }
        }

        private async Task ProcessTrialEndingSubscriptionsAsync(IUnitOfWork unitOfWork, INotificationService notificationService)
        {
            var today = DateTime.UtcNow.Date;

            // Get trial subscriptions that end in 7, 3, and 1 days
            var reminderDays = new[] { 7, 3, 1 };

            foreach (var daysLeft in reminderDays)
            {
                var trialEndDate = today.AddDays(daysLeft);
                var trialEndingSubscriptions = await unitOfWork.Subscriptions.GetAsync(
                    s => s.Status == SubscriptionStatus.Trial &&
                         s.TrialEndDate.HasValue &&
                         s.TrialEndDate.Value.Date == trialEndDate);

                foreach (var subscription in trialEndingSubscriptions)
                {
                    _logger.LogInformation("Sending trial ending reminder for subscription {SubscriptionId}, {DaysLeft} days left",
                        subscription.Id, daysLeft);

                    try
                    {
                        await notificationService.SendTrialEndingReminderAsync(subscription, daysLeft);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending trial ending reminder for subscription {SubscriptionId}",
                            subscription.Id);
                    }
                }
            }

            // Process ended trials
            var endedTrials = await unitOfWork.Subscriptions.GetAsync(
                s => s.Status == SubscriptionStatus.Trial &&
                     s.TrialEndDate.HasValue &&
                     s.TrialEndDate.Value.Date < today);

            foreach (var subscription in endedTrials)
            {
                _logger.LogInformation("Processing ended trial subscription {SubscriptionId}", subscription.Id);

                try
                {
                    // Update subscription status
                    subscription.EnterGracePeriod();
                    await unitOfWork.SaveChangesAsync();

                    // Send notification
                    await notificationService.SendSubscriptionExpiredAsync(subscription);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing ended trial subscription {SubscriptionId}", subscription.Id);
                }
            }
        }
    }
}
