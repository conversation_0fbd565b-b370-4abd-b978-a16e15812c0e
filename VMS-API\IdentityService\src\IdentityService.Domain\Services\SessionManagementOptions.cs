namespace IdentityService.Domain.Services;

/// <summary>
/// Options for session management
/// </summary>
public class SessionManagementOptions
{
    /// <summary>
    /// The number of minutes of inactivity after which a session is considered idle
    /// </summary>
    public int IdleTimeoutMinutes { get; set; } = 30;
    
    /// <summary>
    /// The maximum number of hours a session can be active, regardless of activity
    /// </summary>
    public int AbsoluteTimeoutHours { get; set; } = 24;
    
    /// <summary>
    /// The maximum number of concurrent sessions a user can have
    /// </summary>
    public int MaxConcurrentSessions { get; set; } = 5;
    
    /// <summary>
    /// Whether to prevent concurrent logins (if true, new login terminates existing sessions)
    /// </summary>
    public bool PreventConcurrentLogin { get; set; } = false;
}
