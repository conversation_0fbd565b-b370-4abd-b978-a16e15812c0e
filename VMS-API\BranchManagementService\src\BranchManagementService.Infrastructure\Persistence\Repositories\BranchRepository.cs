using BranchManagementService.Domain.Entities;
using BranchManagementService.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using VMS.Contracts.Common.Context;

namespace BranchManagementService.Infrastructure.Persistence.Repositories
{
    public class BranchRepository : BaseRepository<Branch>, IBranchRepository
    {
        private readonly IBranchContextService _branchContextService;

        public BranchRepository(BranchDbContext context, IBranchContextService branchContextService) : base(context)
        {
            _branchContextService = branchContextService;
        }

        public async Task<IReadOnlyList<Branch>> GetByVendorIdAsync(Guid vendorId)
        {
            var query = _dbSet.Where(b => b.VendorId == vendorId);
            query = ApplyBranchContextFilter(query);
            return await query.ToListAsync();
        }

        public async Task<int> GetBranchCountAsync(Guid vendorId)
        {
            var query = _dbSet.Where(b => b.VendorId == vendorId && b.IsActive);
            query = ApplyBranchContextFilter(query);
            return await query.CountAsync();
        }

        public async Task<bool> IsBranchLimitReachedAsync(Guid vendorId, int limit)
        {
            var count = await GetBranchCountAsync(vendorId);
            return count >= limit;
        }

        public override async Task<Branch?> GetByIdAsync(Guid id)
        {
            var query = _dbSet.Where(b => b.Id == id);
            query = ApplyBranchContextFilter(query);
            return await query.FirstOrDefaultAsync();
        }

        public override async Task<IReadOnlyList<Branch>> GetAllAsync()
        {
            var query = _dbSet.AsQueryable();
            query = ApplyBranchContextFilter(query);
            return await query.ToListAsync();
        }

        private IQueryable<Branch> ApplyBranchContextFilter(IQueryable<Branch> query)
        {
            var branchContext = _branchContextService.GetCurrentBranchContext();

            // Always filter by vendor ID
            query = query.Where(b => b.VendorId == branchContext.VendorId);

            // If not a vendor admin and has a branch ID, filter by branch ID
            if (!branchContext.IsVendorAdmin && branchContext.BranchId.HasValue)
            {
                query = query.Where(b => b.Id == branchContext.BranchId.Value);
            }

            return query;
        }
    }
}
