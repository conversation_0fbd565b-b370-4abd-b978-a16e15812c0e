using Microsoft.AspNetCore.Builder;

namespace BranchManagementService.API.Middleware
{
    /// <summary>
    /// Extension methods for middleware registration
    /// </summary>
    public static class MiddlewareExtensions
    {
        /// <summary>
        /// Adds the branch context middleware to the application pipeline
        /// </summary>
        /// <param name="app">The application builder</param>
        /// <returns>The application builder</returns>
        public static IApplicationBuilder UseBranchContext(this IApplicationBuilder app)
        {
            return app.UseMiddleware<BranchContextMiddleware>();
        }
    }
}
