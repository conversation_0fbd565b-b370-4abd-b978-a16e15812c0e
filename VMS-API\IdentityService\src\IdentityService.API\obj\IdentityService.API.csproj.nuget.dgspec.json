{"format": 1, "restore": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj": {}}, "projects": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj", "projectName": "IdentityService.API", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj", "projectName": "IdentityService.Application", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}, "MediatR.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.1.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj", "projectName": "IdentityService.Domain", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj", "projectName": "IdentityService.Infrastructure", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "MassTransit.RabbitMQ": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj", "projectName": "VMSContracts", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}