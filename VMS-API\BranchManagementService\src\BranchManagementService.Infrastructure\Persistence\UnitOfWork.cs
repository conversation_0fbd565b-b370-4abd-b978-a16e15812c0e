using BranchManagementService.Domain.Interfaces;
using BranchManagementService.Infrastructure.Persistence.Repositories;
using System;
using System.Threading.Tasks;
using VMS.Contracts.Common.Context;

namespace BranchManagementService.Infrastructure.Persistence
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly BranchDbContext _context;
        private readonly IBranchContextService _branchContextService;
        private IBranchRepository? _branchRepository;
        private bool _disposed;

        public UnitOfWork(BranchDbContext context, IBranchContextService branchContextService)
        {
            _context = context;
            _branchContextService = branchContextService;
        }

        public IBranchRepository Branches => _branchRepository ??= new BranchRepository(_context, _branchContextService);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context.Dispose();
            }
            _disposed = true;
        }
    }
}
