using Microsoft.Extensions.Logging;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;
using SubscriptionService.Infrastructure.Messaging.Events;

namespace SubscriptionService.Infrastructure.Services
{
    public class SubscriptionChangeService : ISubscriptionChangeService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPromotionService _promotionService;
        private readonly INotificationService _notificationService;
        private readonly IEventPublisher _eventPublisher;
        private readonly ILogger<SubscriptionChangeService> _logger;

        public SubscriptionChangeService(
            IUnitOfWork unitOfWork,
            IPromotionService promotionService,
            INotificationService notificationService,
            IEventPublisher eventPublisher,
            ILogger<SubscriptionChangeService> logger)
        {
            _unitOfWork = unitOfWork;
            _promotionService = promotionService;
            _notificationService = notificationService;
            _eventPublisher = eventPublisher;
            _logger = logger;
        }

        public async Task<SubscriptionChange> UpgradeSubscriptionAsync(
            Guid subscriptionId,
            Guid newPlanId,
            string? promotionCode = null)
        {
            _logger.LogInformation("Upgrading subscription {SubscriptionId} to plan {NewPlanId}",
                subscriptionId, newPlanId);

            // Get the subscription
            var subscription = await _unitOfWork.Subscriptions.GetSubscriptionWithPlanAsync(subscriptionId);
            if (subscription == null)
            {
                throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
            }

            // Get the new plan
            var newPlan = await _unitOfWork.SubscriptionPlans.GetByIdAsync(newPlanId);
            if (newPlan == null)
            {
                throw new ArgumentException($"Subscription plan with ID {newPlanId} not found");
            }

            // Validate that this is actually an upgrade
            if (newPlan.Tier <= subscription.SubscriptionPlan.Tier)
            {
                throw new ArgumentException("The new plan must be of a higher tier than the current plan");
            }

            // Store the previous plan ID and price
            var previousPlanId = subscription.SubscriptionPlanId;
            var previousPrice = subscription.CurrentPrice;

            // Calculate the new price
            var newPrice = newPlan.Price;

            // Apply promotion code if provided
            if (!string.IsNullOrEmpty(promotionCode))
            {
                if (await _promotionService.ValidatePromotionCodeAsync(promotionCode, newPlanId))
                {
                    var discount = await _promotionService.CalculateDiscountAsync(promotionCode, newPrice);
                    newPrice -= discount;

                    // Use the promotion code
                    await _promotionService.UsePromotionCodeAsync(promotionCode);
                }
                else
                {
                    _logger.LogWarning("Invalid promotion code {PromotionCode} for plan {PlanId}",
                        promotionCode, newPlanId);
                    promotionCode = null;
                }
            }

            // Update the subscription
            subscription.UpdateSubscriptionPlan(newPlanId);
            subscription.UpdatePrice(newPrice);

            // Create the subscription change record
            var change = new SubscriptionChange(
                subscriptionId,
                previousPlanId,
                newPlanId,
                ChangeType.Upgrade,
                DateTime.UtcNow,
                previousPrice,
                newPrice,
                "Subscription upgraded",
                promotionCode);

            // Save the changes
            await _unitOfWork.SubscriptionChanges.AddAsync(change);
            await _unitOfWork.SaveChangesAsync();

            // Send notification
            await _notificationService.SendSubscriptionActivatedAsync(subscription);

            // Publish event
            var @event = new SubscriptionUpgradedEvent(
                subscription.Id,
                subscription.UserId,
                previousPlanId,
                newPlanId,
                previousPrice,
                newPrice);

            await _eventPublisher.PublishAsync(@event);

            return change;
        }

        public async Task<SubscriptionChange> DowngradeSubscriptionAsync(
            Guid subscriptionId,
            Guid newPlanId)
        {
            _logger.LogInformation("Downgrading subscription {SubscriptionId} to plan {NewPlanId}",
                subscriptionId, newPlanId);

            // Get the subscription
            var subscription = await _unitOfWork.Subscriptions.GetSubscriptionWithPlanAsync(subscriptionId);
            if (subscription == null)
            {
                throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
            }

            // Get the new plan
            var newPlan = await _unitOfWork.SubscriptionPlans.GetByIdAsync(newPlanId);
            if (newPlan == null)
            {
                throw new ArgumentException($"Subscription plan with ID {newPlanId} not found");
            }

            // Validate that this is actually a downgrade
            if (newPlan.Tier >= subscription.SubscriptionPlan.Tier)
            {
                throw new ArgumentException("The new plan must be of a lower tier than the current plan");
            }

            // Store the previous plan ID and price
            var previousPlanId = subscription.SubscriptionPlanId;
            var previousPrice = subscription.CurrentPrice;

            // Calculate the new price
            var newPrice = newPlan.Price;

            // Update the subscription
            // Note: Downgrades typically take effect at the end of the current billing cycle
            // For simplicity, we're applying it immediately here
            subscription.UpdateSubscriptionPlan(newPlanId);
            subscription.UpdatePrice(newPrice);

            // Create the subscription change record
            var change = new SubscriptionChange(
                subscriptionId,
                previousPlanId,
                newPlanId,
                ChangeType.Downgrade,
                DateTime.UtcNow,
                previousPrice,
                newPrice,
                "Subscription downgraded");

            // Save the changes
            await _unitOfWork.SubscriptionChanges.AddAsync(change);
            await _unitOfWork.SaveChangesAsync();

            // Send notification
            await _notificationService.SendSubscriptionActivatedAsync(subscription);

            // Publish event
            var @event = new SubscriptionDowngradedEvent(
                subscription.Id,
                subscription.UserId,
                previousPlanId,
                newPlanId,
                previousPrice,
                newPrice);

            await _eventPublisher.PublishAsync(@event);

            return change;
        }

        public async Task<SubscriptionChange> RenewSubscriptionAsync(
            Guid subscriptionId,
            string? promotionCode = null)
        {
            _logger.LogInformation("Renewing subscription {SubscriptionId}", subscriptionId);

            // Get the subscription
            var subscription = await _unitOfWork.Subscriptions.GetSubscriptionWithPlanAsync(subscriptionId);
            if (subscription == null)
            {
                throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
            }

            // Store the previous price
            var previousPrice = subscription.CurrentPrice;

            // Calculate the new price
            var newPrice = subscription.SubscriptionPlan.Price;

            // Apply promotion code if provided
            if (!string.IsNullOrEmpty(promotionCode))
            {
                if (await _promotionService.ValidatePromotionCodeAsync(promotionCode, subscription.SubscriptionPlanId))
                {
                    var discount = await _promotionService.CalculateDiscountAsync(promotionCode, newPrice);
                    newPrice -= discount;

                    // Use the promotion code
                    await _promotionService.UsePromotionCodeAsync(promotionCode);
                }
                else
                {
                    _logger.LogWarning("Invalid promotion code {PromotionCode} for plan {PlanId}",
                        promotionCode, subscription.SubscriptionPlanId);
                    promotionCode = null;
                }
            }

            // Update the subscription
            var oldEndDate = subscription.EndDate;
            var newEndDate = CalculateNewEndDate(subscription.EndDate, subscription.BillingCycle);
            subscription.UpdateEndDate(newEndDate);
            subscription.UpdatePrice(newPrice);

            // Create the subscription change record
            var change = new SubscriptionChange(
                subscriptionId,
                subscription.SubscriptionPlanId,
                subscription.SubscriptionPlanId,
                ChangeType.Renewal,
                DateTime.UtcNow,
                previousPrice,
                newPrice,
                "Subscription renewed",
                promotionCode);

            // Save the changes
            await _unitOfWork.SubscriptionChanges.AddAsync(change);
            await _unitOfWork.SaveChangesAsync();

            // Send notification
            await _notificationService.SendSubscriptionActivatedAsync(subscription);

            // Publish event
            var @event = new SubscriptionRenewedEvent(
                subscription.Id,
                subscription.UserId,
                oldEndDate,
                newEndDate,
                newPrice);

            await _eventPublisher.PublishAsync(@event);

            return change;
        }

        public async Task<SubscriptionChange> ConvertTrialAsync(
            Guid subscriptionId,
            Guid planId,
            string? promotionCode = null)
        {
            _logger.LogInformation("Converting trial subscription {SubscriptionId} to plan {PlanId}",
                subscriptionId, planId);

            // Get the subscription
            var subscription = await _unitOfWork.Subscriptions.GetSubscriptionWithPlanAsync(subscriptionId);
            if (subscription == null)
            {
                throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
            }

            // Verify that the subscription is in trial status
            if (subscription.Status != SubscriptionStatus.Trial)
            {
                throw new ArgumentException("Only trial subscriptions can be converted");
            }

            // Get the new plan
            var newPlan = await _unitOfWork.SubscriptionPlans.GetByIdAsync(planId);
            if (newPlan == null)
            {
                throw new ArgumentException($"Subscription plan with ID {planId} not found");
            }

            // Store the previous plan ID and price
            var previousPlanId = subscription.SubscriptionPlanId;
            var previousPrice = subscription.CurrentPrice;

            // Calculate the new price
            var newPrice = newPlan.Price;

            // Apply promotion code if provided
            if (!string.IsNullOrEmpty(promotionCode))
            {
                if (await _promotionService.ValidatePromotionCodeAsync(promotionCode, planId))
                {
                    var discount = await _promotionService.CalculateDiscountAsync(promotionCode, newPrice);
                    newPrice -= discount;

                    // Use the promotion code
                    await _promotionService.UsePromotionCodeAsync(promotionCode);
                }
                else
                {
                    _logger.LogWarning("Invalid promotion code {PromotionCode} for plan {PlanId}",
                        promotionCode, planId);
                    promotionCode = null;
                }
            }

            // Update the subscription
            subscription.UpdateSubscriptionPlan(planId);
            subscription.UpdatePrice(newPrice);
            subscription.Activate();

            // Create the subscription change record
            var change = new SubscriptionChange(
                subscriptionId,
                previousPlanId,
                planId,
                ChangeType.TrialConversion,
                DateTime.UtcNow,
                previousPrice,
                newPrice,
                "Trial converted to paid subscription",
                promotionCode);

            // Save the changes
            await _unitOfWork.SubscriptionChanges.AddAsync(change);
            await _unitOfWork.SaveChangesAsync();

            // Send notification
            await _notificationService.SendSubscriptionActivatedAsync(subscription);

            // Publish event
            var @event = new TrialConvertedEvent(
                subscription.Id,
                subscription.UserId,
                planId,
                newPrice);

            await _eventPublisher.PublishAsync(@event);

            return change;
        }

        public async Task<IReadOnlyList<SubscriptionChange>> GetSubscriptionChangeHistoryAsync(Guid subscriptionId)
        {
            return await _unitOfWork.SubscriptionChanges.GetChangesBySubscriptionIdAsync(subscriptionId);
        }

        private DateTime CalculateNewEndDate(DateTime currentEndDate, BillingCycle billingCycle)
        {
            return billingCycle switch
            {
                BillingCycle.Monthly => currentEndDate.AddMonths(1),
                BillingCycle.Quarterly => currentEndDate.AddMonths(3),
                BillingCycle.Yearly => currentEndDate.AddYears(1),
                _ => currentEndDate.AddMonths(1)
            };
        }
    }
}
