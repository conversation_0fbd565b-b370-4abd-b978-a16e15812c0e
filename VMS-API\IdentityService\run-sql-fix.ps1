# Script to run the SQL fix for adding the PrimaryBranchId column

# Get the connection string from appsettings.json
$appsettingsPath = Join-Path $PSScriptRoot "src\IdentityService.API\appsettings.json"
$appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
$connectionString = $appsettings.ConnectionStrings.DefaultConnection

Write-Host "Connection string: $connectionString" -ForegroundColor Cyan

# Check if psql is available
try {
    $psqlVersion = & psql --version
    Write-Host "PostgreSQL client found: $psqlVersion" -ForegroundColor Green
    
    # Extract connection parameters from the connection string
    $host = ($connectionString -split ";" | Where-Object { $_ -like "Host=*" }).Substring(5)
    $port = ($connectionString -split ";" | Where-Object { $_ -like "Port=*" }).Substring(5)
    $database = ($connectionString -split ";" | Where-Object { $_ -like "Database=*" }).Substring(9)
    $username = ($connectionString -split ";" | Where-Object { $_ -like "Username=*" }).Substring(9)
    $password = ($connectionString -split ";" | Where-Object { $_ -like "Password=*" }).Substring(9)
    
    # Set PGPASSWORD environment variable
    $env:PGPASSWORD = $password
    
    # Run the SQL script
    $sqlScriptPath = Join-Path $PSScriptRoot "add-primary-branch-column.sql"
    Write-Host "Running SQL script: $sqlScriptPath" -ForegroundColor Yellow
    
    & psql -h $host -p $port -d $database -U $username -f $sqlScriptPath
    
    # Clear the password from environment
    $env:PGPASSWORD = ""
    
    Write-Host "SQL script executed successfully." -ForegroundColor Green
}
catch {
    Write-Host "PostgreSQL client (psql) not found or error running script." -ForegroundColor Red
    Write-Host "Please install PostgreSQL client tools or run the SQL script manually." -ForegroundColor Yellow
    Write-Host "Error: $_" -ForegroundColor Red
}
