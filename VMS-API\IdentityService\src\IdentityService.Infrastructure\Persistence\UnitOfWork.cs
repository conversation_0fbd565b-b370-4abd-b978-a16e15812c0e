using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Domain.Common;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Infrastructure.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace IdentityService.Infrastructure.Persistence
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _context;
        private IDbContextTransaction? _transaction;

        public IUserRepository UserRepository { get; }
        public IRepository<Role> RoleRepository { get; }
        public IRepository<Permission> PermissionRepository { get; }
        public IRepository<UserRole> UserRoleRepository { get; }
        public IRepository<RolePermission> RolePermissionRepository { get; }
        public IRepository<PasswordResetToken> PasswordResetTokenRepository { get; }
        public IAuditLogRepository AuditLogRepository { get; }
        public IRepository<Menu> MenuRepository { get; }
        public IRepository<MenuPermission> MenuPermissionRepository { get; }
        public IUserSubscriptionRepository UserSubscriptionRepository { get; }
        public IRepository<SubscriptionFeature> SubscriptionFeatureRepository { get; }
        public IUserSessionRepository UserSessionRepository { get; }
        public IRepository<UserLoginHistory> UserLoginHistoryRepository { get; }

        public UnitOfWork(ApplicationDbContext context)
        {
            _context = context;
            UserRepository = new UserRepository(_context);
            RoleRepository = new GenericRepositoryImpl<Role>(_context);
            PermissionRepository = new GenericRepositoryImpl<Permission>(_context);
            UserRoleRepository = new GenericRepositoryImpl<UserRole>(_context);
            RolePermissionRepository = new GenericRepositoryImpl<RolePermission>(_context);
            PasswordResetTokenRepository = new GenericRepositoryImpl<PasswordResetToken>(_context);
            AuditLogRepository = new AuditLogRepository(_context);
            MenuRepository = new GenericRepositoryImpl<Menu>(_context);
            MenuPermissionRepository = new GenericRepositoryImpl<MenuPermission>(_context);
            UserSubscriptionRepository = new UserSubscriptionRepository(_context);
            SubscriptionFeatureRepository = new GenericRepositoryImpl<SubscriptionFeature>(_context);
            UserSessionRepository = new UserSessionRepository(_context);
            UserLoginHistoryRepository = new GenericRepositoryImpl<UserLoginHistory>(_context);
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return await _context.SaveChangesAsync(cancellationToken);
            }
            catch (DbUpdateConcurrencyException ex)
            {
                // Handle concurrency conflicts
                foreach (var entry in ex.Entries)
                {
                    var databaseValues = await entry.GetDatabaseValuesAsync(cancellationToken);

                    if (databaseValues == null)
                    {
                        // The entity was deleted by another user
                        throw new InvalidOperationException($"The record you attempted to edit was deleted by another user. Entity: {entry.Entity.GetType().Name}");
                    }

                    // For new entities (Added state), we don't need to refresh original values
                    if (entry.State != EntityState.Added)
                    {
                        // Refresh the original values to reflect the database values
                        entry.OriginalValues.SetValues(databaseValues);
                    }
                }

                // Retry the save once
                try
                {
                    return await _context.SaveChangesAsync(cancellationToken);
                }
                catch (DbUpdateConcurrencyException)
                {
                    // If retry fails, rethrow the original exception to let the caller handle it
                    throw ex;
                }
            }
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction == null)
                _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }

    public class GenericRepositoryImpl<T> : GenericRepository<T>, IRepository<T> where T : BaseEntity
    {
        public GenericRepositoryImpl(ApplicationDbContext context) : base(context) { }
    }
}
