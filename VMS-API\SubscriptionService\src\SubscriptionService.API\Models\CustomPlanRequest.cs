using System;
using System.ComponentModel.DataAnnotations;

namespace SubscriptionService.API.Models
{
    public class CustomPlanRequest
    {
        [Required]
        public Guid BasePlanId { get; set; }
        
        [Required]
        [Range(1, 100, ErrorMessage = "Branch count must be between 1 and 100")]
        public int BranchCount { get; set; }
        
        [Required]
        [Range(1, 100, ErrorMessage = "Users per branch must be between 1 and 100")]
        public int UsersPerBranch { get; set; }
    }
}
