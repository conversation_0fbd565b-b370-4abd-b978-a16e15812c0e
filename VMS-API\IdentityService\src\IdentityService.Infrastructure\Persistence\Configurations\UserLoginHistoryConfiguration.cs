using IdentityService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace IdentityService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration for the UserLoginHistory entity
/// </summary>
public class UserLoginHistoryConfiguration : IEntityTypeConfiguration<UserLoginHistory>
{
    public void Configure(EntityTypeBuilder<UserLoginHistory> builder)
    {
        builder.HasKey(h => h.Id);

        builder.Property(h => h.Email)
            .IsRequired()
            .HasMaxLength(256);

        builder.Property(h => h.PhoneNumber)
            .HasMaxLength(20);

        builder.Property(h => h.FailureReason)
            .HasMaxLength(1024);

        builder.Property(h => h.IpAddress)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(h => h.UserAgent)
            .IsRequired()
            .HasMaxLength(1024);

        builder.Property(h => h.DeviceInfo)
            .HasMaxLength(1024);

        builder.Property(h => h.Location)
            .HasMaxLength(1024);

        // Configure relationships
        builder.HasOne(h => h.User)
            .WithMany()
            .HasForeignKey(h => h.UserId)
            .OnDelete(DeleteBehavior.SetNull);

        // Add indexes
        builder.HasIndex(h => h.UserId);
        builder.HasIndex(h => h.Email);
        builder.HasIndex(h => h.IsSuccessful);
        builder.HasIndex(h => h.CreatedAt);
        builder.HasIndex(h => h.IpAddress);
    }
}
