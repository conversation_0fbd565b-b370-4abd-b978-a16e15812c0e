using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Auth.Commands;

public class LogoutCommand : BaseRequest<bool>
{
    public string Token { get; set; }
    public bool LogoutAllSessions { get; set; }
}

public class LogoutCommandHandler : IRequestHandler<LogoutCommand, bool>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<LogoutCommandHandler> _logger;
    private readonly ISessionManagementService _sessionManagementService;
    private readonly ICurrentUserService _currentUserService;

    public LogoutCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<LogoutCommandHandler> logger,
        ISessionManagementService sessionManagementService,
        ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _sessionManagementService = sessionManagementService;
        _currentUserService = currentUserService;
    }

    public async Task<bool> Handle(LogoutCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
            {
                _logger.LogWarning("User ID not found in token during logout");
                return false;
            }

            if (request.LogoutAllSessions)
            {
                // End all sessions except the current one
                await _sessionManagementService.EndAllSessionsForUserAsync(userId.Value, request.Token);
                _logger.LogInformation("User {UserId} logged out of all sessions", userId.Value);
            }
            else
            {
                // End only the current session
                await _sessionManagementService.EndSessionAsync(request.Token);
                _logger.LogInformation("User {UserId} logged out of session with token {Token}", userId.Value, request.Token);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return false;
        }
    }
}
