using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.Json;
using System.IO;

namespace BranchManagementService.Infrastructure.Persistence
{
    public class BranchDbContextFactory : IDesignTimeDbContextFactory<BranchDbContext>
    {
        public BranchDbContext CreateDbContext(string[] args)
        {
            // Get the current directory
            var currentDirectory = Directory.GetCurrentDirectory();

            // Navigate to the API project directory if needed
            string apiProjectPath = currentDirectory;
            if (currentDirectory.Contains("Infrastructure"))
            {
                apiProjectPath = Path.GetFullPath(Path.Combine(currentDirectory, "..", "BranchManagementService.API"));
            }

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile(Path.Combine(apiProjectPath, "appsettings.json"), optional: false, reloadOnChange: true)
                .AddJsonFile(Path.Combine(apiProjectPath, "appsettings.Development.json"), optional: true, reloadOnChange: true)
                .Build();

            // Get connection string
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            // Create DbContext options
            var optionsBuilder = new DbContextOptionsBuilder<BranchDbContext>();
            optionsBuilder.UseNpgsql(connectionString,
                b => b.MigrationsAssembly("BranchManagementService.Infrastructure"));

            return new BranchDbContext(optionsBuilder.Options);
        }
    }
}
