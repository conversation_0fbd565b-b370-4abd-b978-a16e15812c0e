using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Application.Features.Users.Queries;
using IdentityService.Application.Features.Roles.Queries;
using IdentityService.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers
{
    /// <summary>
    /// Controller for service-to-service API calls
    /// </summary>
    [ApiController]
    [Route("api/service")]
    [Authorize(AuthenticationSchemes = "Bearer")]
    public class ServiceApiController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ServiceApiController> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceApiController"/> class
        /// </summary>
        /// <param name="mediator">The mediator</param>
        /// <param name="logger">The logger</param>
        public ServiceApiController(IMediator mediator, ILogger<ServiceApiController> logger)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a user by ID
        /// </summary>
        /// <param name="id">The user ID</param>
        /// <returns>The user details</returns>
        [HttpGet("users/{id}")]
        public async Task<IActionResult> GetUserById(Guid id)
        {
            try
            {
                _logger.LogInformation("Service API: Getting user with ID {UserId}", id);

                var query = new GetUserByIdQuery { UserId = id };
                var user = await _mediator.Send(query);

                if (user == null)
                {
                    return NotFound();
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user with ID {UserId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the user" });
            }
        }

        /// <summary>
        /// Gets users by IDs
        /// </summary>
        /// <param name="userIds">The user IDs</param>
        /// <returns>The user details</returns>
        [HttpPost("users/batch")]
        public async Task<IActionResult> GetUsersByIds([FromBody] List<Guid> userIds)
        {
            try
            {
                _logger.LogInformation("Service API: Getting users by IDs, count: {Count}", userIds.Count);

                var query = new GetUsersByIdsQuery { UserIds = userIds };
                var users = await _mediator.Send(query);

                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users by IDs");
                return StatusCode(500, new { message = "An error occurred while retrieving the users" });
            }
        }

        /// <summary>
        /// Gets a user by email
        /// </summary>
        /// <param name="email">The user's email</param>
        /// <returns>The user details</returns>
        [HttpGet("users/by-email")]
        public async Task<IActionResult> GetUserByEmail([FromQuery] string email)
        {
            try
            {
                _logger.LogInformation("Service API: Getting user by email {Email}", email);

                var query = new GetUserByEmailQuery { Email = email };
                var user = await _mediator.Send(query);

                if (user == null)
                {
                    return NotFound();
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email {Email}", email);
                return StatusCode(500, new { message = "An error occurred while retrieving the user" });
            }
        }

        /// <summary>
        /// Checks if a user has a specific role
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="roleName">The role name</param>
        /// <returns>True if the user has the role, false otherwise</returns>
        [HttpGet("users/{userId}/has-role")]
        public async Task<IActionResult> UserHasRole(Guid userId, [FromQuery] string roleName)
        {
            try
            {
                _logger.LogInformation("Service API: Checking if user {UserId} has role {RoleName}", userId, roleName);

                var query = new CheckUserHasRoleQuery { UserId = userId, RoleName = roleName };
                var hasRole = await _mediator.Send(query);

                return Ok(hasRole);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} has role {RoleName}", userId, roleName);
                return StatusCode(500, new { message = "An error occurred while checking the user's role" });
            }
        }

        /// <summary>
        /// Gets the roles for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>The user's roles</returns>
        [HttpGet("users/{userId}/roles")]
        public async Task<IActionResult> GetUserRoles(Guid userId)
        {
            try
            {
                _logger.LogInformation("Service API: Getting roles for user {UserId}", userId);

                var query = new GetUserRolesQuery { UserId = userId };
                var roles = await _mediator.Send(query);

                return Ok(roles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
                return StatusCode(500, new { message = "An error occurred while retrieving the user's roles" });
            }
        }
    }
}
