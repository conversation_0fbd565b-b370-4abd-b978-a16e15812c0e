using System;
using System.Collections.Generic;
using SubscriptionService.Domain.Enums;

namespace SubscriptionService.API.Models
{
    public class SubscriptionSummary
    {
        public Guid VendorId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DurationInMonths { get; set; }
        public int GracePeriodDays { get; set; }
        public BillingCycle BillingCycle { get; set; }
        public string? AppliedPromotionCode { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal GrandTotal { get; set; }
        public string FormattedGrandTotal { get; set; } = string.Empty;
        public List<PlanSummary> SelectedPlans { get; set; } = new List<PlanSummary>();
    }

    public class PlanSummary
    {
        public Guid PlanId { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public string PlanDescription { get; set; } = string.Empty;
        public SubscriptionTier Tier { get; set; }
        public int BranchCount { get; set; }
        public int UsersPerBranch { get; set; }
        public int TotalUsers { get; set; }
        public int StandardUsersPerBranch { get; set; }
        public int ExtraUsersPerBranch { get; set; }
        public decimal BasePerBranchPrice { get; set; }
        public decimal PricePerExtraUser { get; set; }
        public decimal PricePerBranch { get; set; }
        public decimal TotalPrice { get; set; }
        public string FormattedBasePrice { get; set; } = string.Empty;
        public string FormattedPricePerBranch { get; set; } = string.Empty;
        public string FormattedTotalPrice { get; set; } = string.Empty;
        public List<FeatureSummary> Features { get; set; } = new List<FeatureSummary>();
    }

    public class FeatureSummary
    {
        public string FeatureName { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
        public int? UsageLimit { get; set; }
    }
}
