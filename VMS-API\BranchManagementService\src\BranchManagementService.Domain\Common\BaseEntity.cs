using System;

namespace BranchManagementService.Domain.Common
{
    public abstract class BaseEntity
    {
        public Guid Id { get; protected set; }
        public string CreatedBy { get; protected set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string? UpdatedBy { get; protected set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
