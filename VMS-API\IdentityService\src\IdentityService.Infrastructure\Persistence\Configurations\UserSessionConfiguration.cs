using IdentityService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace IdentityService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration for the UserSession entity
/// </summary>
public class UserSessionConfiguration : IEntityTypeConfiguration<UserSession>
{
    public void Configure(EntityTypeBuilder<UserSession> builder)
    {
        builder.HasKey(s => s.Id);

        builder.Property(s => s.Token)
            .IsRequired()
            .HasMaxLength(1024);

        builder.Property(s => s.RefreshToken)
            .IsRequired()
            .HasMaxLength(1024);

        builder.Property(s => s.IpAddress)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(s => s.UserAgent)
            .IsRequired()
            .HasMaxLength(1024);

        builder.Property(s => s.DeviceInfo)
            .HasMaxLength(1024);

        builder.Property(s => s.EndReason)
            .HasMaxLength(1024);

        // Configure relationships
        builder.HasOne(s => s.User)
            .WithMany()
            .HasForeignKey(s => s.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure concurrency token
        builder.Property(s => s.RowVersion)
            .IsRowVersion()
            .IsConcurrencyToken()
            .ValueGeneratedOnAddOrUpdate() // Ensure it's generated by the database
            .IsRequired(false); // Allow null values to fix the constraint issue

        // Add indexes
        builder.HasIndex(s => s.Token);
        builder.HasIndex(s => s.RefreshToken);
        builder.HasIndex(s => s.UserId);
        builder.HasIndex(s => s.Status);
        builder.HasIndex(s => s.ExpiresAt);
    }
}
