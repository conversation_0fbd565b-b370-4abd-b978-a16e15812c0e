2025-05-08 16:26:29.869 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-08 16:26:29.955 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-08 16:26:29.968 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-08 16:26:29.970 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-08 16:26:29.972 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-08 16:26:29.974 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-08 16:26:30.984 +05:30 [INF] Executed DbCommand (73ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-08 16:26:31.036 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-08 16:26:31.156 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-08 16:26:31.305 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-08 16:26:31.339 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-08 16:26:31.402 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-08 16:26:31.479 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-08 16:26:31.851 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-08 16:26:31.859 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-08 16:26:32.117 +05:30 [DBG] Starting bus instances: IBus
2025-05-08 16:26:32.123 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-08 16:26:32.163 +05:30 [INF] Session cleanup service is starting
2025-05-08 16:26:32.167 +05:30 [DBG] Starting session cleanup
2025-05-08 16:26:32.207 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-08 16:26:32.354 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 63554)
2025-05-08 16:26:32.402 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_1broyyfpkikqnwtzbdqah8z78p?temporary=true"
2025-05-08 16:26:32.453 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-08 16:26:32.453 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-08 16:26:32.487 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-08 16:26:32.499 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-08 16:26:32.668 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-08 16:26:32.669 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-08 16:26:32.706 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-08 16:26:32.706 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-08 16:26:32.734 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-08 16:26:32.735 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-08 16:26:32.790 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-ahLvtqW-dqMioVMVmzU-wA
2025-05-08 16:26:32.790 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-YWVLDOFZ-PDYeZ-RYNSWRA
2025-05-08 16:26:32.797 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-08 16:26:32.798 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-08 16:26:32.801 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-08 16:26:33.015 +05:30 [INF] Executed DbCommand (33ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-08 16:26:33.063 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-08 16:26:33.082 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-08 16:26:33.094 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-08 16:26:33.096 +05:30 [INF] Hosting environment: Development
2025-05-08 16:26:33.097 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-08 16:26:33.098 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-08 16:26:33.100 +05:30 [DBG] Session cleanup completed
2025-05-08 16:26:45.660 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-08 16:26:45.747 +05:30 [INF] Request was redirected to /swagger
2025-05-08 16:26:45.753 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 98.7628ms
2025-05-08 16:26:45.765 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger - null null
2025-05-08 16:26:45.828 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger - 301 0 null 62.0826ms
2025-05-08 16:26:45.832 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-08 16:26:45.934 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 101.3368ms
2025-05-08 16:26:45.963 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui.css - null null
2025-05-08 16:26:45.967 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-bundle.js - null null
2025-05-08 16:26:45.968 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-standalone-preset.js - null null
2025-05-08 16:26:46.012 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-08 16:26:46.026 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-08 16:26:46.047 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-08 16:26:46.070 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui.css - 200 143943 text/css 105.8838ms
2025-05-08 16:26:46.082 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-standalone-preset.js - 200 339486 text/javascript 113.733ms
2025-05-08 16:26:46.087 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-bundle.js - 200 1096145 text/javascript 119.8967ms
2025-05-08 16:26:46.364 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-08 16:26:46.633 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 269.8276ms
2025-05-08 16:27:40.407 +05:30 [INF] Application is shutting down...
2025-05-08 16:27:40.434 +05:30 [DBG] Stopping bus instances: IBus
2025-05-08 16:27:40.438 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-05-08 16:27:40.448 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionCreated"
2025-05-08 16:27:40.455 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionCreated" (Stop Receive Transport)
2025-05-08 16:27:40.466 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-ahLvtqW-dqMioVMVmzU-wA
2025-05-08 16:27:40.467 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-08 16:27:40.469 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionStatusChanged" (Stop Receive Transport)
2025-05-08 16:27:40.473 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionCreated"
2025-05-08 16:27:40.473 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-YWVLDOFZ-PDYeZ-RYNSWRA
2025-05-08 16:27:40.473 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-08 16:27:40.492 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionStatusChanged": 0 received, 0 concurrent, amq.ctag-YWVLDOFZ-PDYeZ-RYNSWRA
2025-05-08 16:27:40.492 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionCreated": 0 received, 0 concurrent, amq.ctag-ahLvtqW-dqMioVMVmzU-wA
2025-05-08 16:27:40.505 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_1broyyfpkikqnwtzbdqah8z78p?temporary=true"
2025-05-08 16:27:40.507 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_1broyyfpkikqnwtzbdqah8z78p?temporary=true"
2025-05-08 16:27:40.514 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-05-08 16:27:40.518 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-05-08 16:27:40.520 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
