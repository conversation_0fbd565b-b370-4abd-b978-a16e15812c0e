using System;
using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SubscriptionService.Application.DTOs;
using SubscriptionService.Application.Features.Subscriptions.Queries;
using SubscriptionService.Application.Features.SubscriptionPlans.Queries;
using SubscriptionService.Application.Features.TermsAndConditions.Queries;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.API.Controllers
{
    /// <summary>
    /// Controller for service-to-service API calls
    /// </summary>
    [ApiController]
    [Route("api/service")]
    [Authorize]
    public class ServiceApiController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMediator _mediator;
        private readonly IMapper _mapper;
        private readonly ILogger<ServiceApiController> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceApiController"/> class
        /// </summary>
        /// <param name="unitOfWork">The unit of work</param>
        /// <param name="mediator">The mediator</param>
        /// <param name="mapper">The mapper</param>
        /// <param name="logger">The logger</param>
        public ServiceApiController(
            IUnitOfWork unitOfWork,
            IMediator mediator,
            IMapper mapper,
            ILogger<ServiceApiController> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a subscription by ID
        /// </summary>
        /// <param name="id">The subscription ID</param>
        /// <returns>The subscription details</returns>
        [HttpGet("subscriptions/{id}")]
        public async Task<IActionResult> GetSubscriptionById(Guid id)
        {
            try
            {
                _logger.LogInformation("Service API: Getting subscription with ID {SubscriptionId}", id);

                var query = new GetSubscriptionByIdQuery { SubscriptionId = id };
                var subscription = await _mediator.Send(query);

                if (subscription == null)
                {
                    return NotFound();
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription with ID {SubscriptionId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the subscription" });
            }
        }

        /// <summary>
        /// Gets a subscription by user ID
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>The subscription details</returns>
        [HttpGet("subscriptions/by-user/{userId}")]
        public async Task<IActionResult> GetSubscriptionByUserId(Guid userId)
        {
            try
            {
                _logger.LogInformation("Service API: Getting subscription for user {UserId}", userId);

                var query = new GetUserSubscriptionsQuery { UserId = userId };
                var subscriptions = await _mediator.Send(query);

                if (subscriptions == null || subscriptions.Count == 0)
                {
                    return NotFound();
                }

                return Ok(subscriptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription for user {UserId}", userId);
                return StatusCode(500, new { message = "An error occurred while retrieving the subscription" });
            }
        }

        /// <summary>
        /// Gets all subscription plans
        /// </summary>
        /// <returns>The subscription plans</returns>
        [HttpGet("subscription-plans")]
        public async Task<IActionResult> GetSubscriptionPlans()
        {
            try
            {
                _logger.LogInformation("Service API: Getting all subscription plans");

                var query = new GetAllSubscriptionPlansQuery { ActiveOnly = true };
                var plans = await _mediator.Send(query);

                return Ok(plans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription plans");
                return StatusCode(500, new { message = "An error occurred while retrieving the subscription plans" });
            }
        }

        /// <summary>
        /// Gets a subscription plan by ID
        /// </summary>
        /// <param name="id">The plan ID</param>
        /// <returns>The subscription plan details</returns>
        [HttpGet("subscription-plans/{id}")]
        public async Task<IActionResult> GetSubscriptionPlanById(Guid id)
        {
            try
            {
                _logger.LogInformation("Service API: Getting subscription plan with ID {PlanId}", id);

                var query = new GetSubscriptionPlanByIdQuery { PlanId = id };
                var plan = await _mediator.Send(query);

                if (plan == null)
                {
                    return NotFound();
                }

                return Ok(plan);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription plan with ID {PlanId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the subscription plan" });
            }
        }

        /// <summary>
        /// Checks if a subscription is active
        /// </summary>
        /// <param name="id">The subscription ID</param>
        /// <returns>True if the subscription is active, false otherwise</returns>
        [HttpGet("subscriptions/{id}/is-active")]
        public async Task<IActionResult> IsSubscriptionActive(Guid id)
        {
            try
            {
                _logger.LogInformation("Service API: Checking if subscription {SubscriptionId} is active", id);

                var subscription = await _unitOfWork.Subscriptions.GetByIdAsync(id);

                if (subscription == null)
                {
                    return NotFound();
                }

                var isActive = subscription.Status == Domain.Enums.SubscriptionStatus.Active;

                return Ok(isActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if subscription {SubscriptionId} is active", id);
                return StatusCode(500, new { message = "An error occurred while checking if the subscription is active" });
            }
        }

        /// <summary>
        /// Gets the latest terms and conditions
        /// </summary>
        /// <returns>The latest terms and conditions</returns>
        [HttpGet("terms-and-conditions/latest")]
        public async Task<IActionResult> GetLatestTermsAndConditions()
        {
            try
            {
                _logger.LogInformation("Service API: Getting latest terms and conditions");

                var query = new GetLatestTermsAndConditionsQuery();
                var terms = await _mediator.Send(query);

                if (terms == null)
                {
                    return NotFound();
                }

                return Ok(terms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting latest terms and conditions");
                return StatusCode(500, new { message = "An error occurred while retrieving the latest terms and conditions" });
            }
        }
    }
}
